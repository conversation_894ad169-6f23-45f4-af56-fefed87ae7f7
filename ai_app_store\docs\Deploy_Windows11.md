# AI App Store Windows 11 部署指南

**版本**: 1.0  
**作者**: Manus AI  
**创建日期**: 2025年7月24日  
**最后更新**: 2025年7月24日

## 目录

1. [系统要求](#系统要求)
2. [环境准备](#环境准备)
3. [项目部署](#项目部署)
4. [数据库配置](#数据库配置)
5. [运行应用](#运行应用)
6. [生产环境部署](#生产环境部署)
7. [常见问题](#常见问题)
8. [性能优化](#性能优化)

## 系统要求

### 最低系统要求

- **操作系统**: Windows 11 (21H2 或更高版本)
- **处理器**: Intel Core i3 或 AMD Ryzen 3 (推荐 i5/Ryzen 5 或更高)
- **内存**: 4GB RAM (推荐 8GB 或更高)
- **存储空间**: 2GB 可用磁盘空间
- **网络**: 稳定的互联网连接

### 推荐系统配置

- **操作系统**: Windows 11 Pro (22H2 或更高版本)
- **处理器**: Intel Core i5-12400 或 AMD Ryzen 5 5600X 或更高
- **内存**: 16GB RAM 或更高
- **存储空间**: 10GB 可用磁盘空间 (SSD 推荐)
- **网络**: 高速宽带连接

## 环境准备

### 1. 安装 Python

#### 1.1 下载 Python

1. 访问 Python 官方网站：https://www.python.org/downloads/
2. 下载 Python 3.11 或更高版本的 Windows 安装包
3. 选择 "Windows installer (64-bit)" 版本

#### 1.2 安装 Python

1. 运行下载的安装程序
2. **重要**: 勾选 "Add Python to PATH" 选项
3. 选择 "Customize installation"
4. 确保以下选项被勾选：
   - pip
   - tcl/tk and IDLE
   - Python test suite
   - py launcher
5. 在 "Advanced Options" 中：
   - 勾选 "Install for all users"
   - 勾选 "Add Python to environment variables"
   - 选择安装路径（推荐默认路径）
6. 点击 "Install" 开始安装

#### 1.3 验证 Python 安装

打开命令提示符 (cmd) 或 PowerShell，运行以下命令：

```cmd
python --version
pip --version
```

应该显示类似以下输出：
```
Python 3.11.0
pip 22.3.1 from C:\Users\<USER>\Python\Python311\lib\site-packages\pip (python 3.11)
```

### 2. 安装 Git

#### 2.1 下载 Git

1. 访问 Git 官方网站：https://git-scm.com/download/win
2. 下载最新版本的 Git for Windows

#### 2.2 安装 Git

1. 运行下载的安装程序
2. 使用默认设置进行安装
3. 在 "Choosing the default editor" 步骤中，可以选择你喜欢的编辑器

#### 2.3 验证 Git 安装

```cmd
git --version
```

### 3. 安装代码编辑器 (可选但推荐)

推荐安装以下编辑器之一：

- **Visual Studio Code**: https://code.visualstudio.com/
- **PyCharm Community**: https://www.jetbrains.com/pycharm/download/
- **Sublime Text**: https://www.sublimetext.com/

## 项目部署

### 1. 获取项目代码

#### 方法一：从 ZIP 文件解压

1. 将项目 ZIP 文件解压到合适的目录，例如：
   ```
   C:\Projects\ai_app_store
   ```

#### 方法二：从 Git 仓库克隆 (如果有)

```cmd
cd C:\Projects
git clone <repository-url> ai_app_store
cd ai_app_store
```

### 2. 创建虚拟环境

打开命令提示符或 PowerShell，导航到项目目录：

```cmd
cd C:\Projects\ai_app_store
```

创建虚拟环境：

```cmd
python -m venv venv
```

激活虚拟环境：

```cmd
# 使用 cmd
venv\Scripts\activate

# 使用 PowerShell
venv\Scripts\Activate.ps1
```

**注意**: 如果在 PowerShell 中遇到执行策略错误，请运行：
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

激活成功后，命令提示符前会显示 `(venv)`。

### 3. 安装项目依赖

确保虚拟环境已激活，然后安装依赖：

```cmd
pip install -r requirements.txt
```

如果遇到网络问题，可以使用国内镜像：

```cmd
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 4. 验证安装

检查关键依赖是否安装成功：

```cmd
pip list | findstr Flask
pip list | findstr SQLAlchemy
```

## 数据库配置

### 1. SQLite 配置 (开发环境)

项目默认使用 SQLite 数据库，无需额外配置。数据库文件将自动创建在：
```
src\database\app.db
```

### 2. PostgreSQL 配置 (生产环境推荐)

#### 2.1 安装 PostgreSQL

1. 访问 PostgreSQL 官网：https://www.postgresql.org/download/windows/
2. 下载并安装 PostgreSQL
3. 记住设置的密码（用于 postgres 用户）

#### 2.2 创建数据库

打开 pgAdmin 或使用命令行：

```sql
CREATE DATABASE ai_app_store;
CREATE USER app_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE ai_app_store TO app_user;
```

#### 2.3 更新配置

修改 `src/main.py` 中的数据库连接字符串：

```python
# 将 SQLite 配置
app.config['SQLALCHEMY_DATABASE_URI'] = f"sqlite:///{os.path.join(os.path.dirname(__file__), 'database', 'app.db')}"

# 替换为 PostgreSQL 配置
app.config['SQLALCHEMY_DATABASE_URI'] = 'postgresql://app_user:your_password@localhost:5432/ai_app_store'
```

安装 PostgreSQL 驱动：

```cmd
pip install psycopg2-binary
```

## 运行应用

### 1. 开发环境运行

确保虚拟环境已激活，然后运行：

```cmd
cd src
python main.py
```

应用将在 http://localhost:5000 启动。

### 2. 验证应用运行

打开浏览器访问：
- 主页: http://localhost:5000
- API 测试: http://localhost:5000/api/users

或使用 curl 测试 (需要安装 curl)：

```cmd
curl http://localhost:5000/api/users
```

### 3. 运行测试

运行单元测试：

```cmd
python -m unittest tests.test_api -v
```

运行 curl 测试脚本 (需要安装 Git Bash 或 WSL)：

```bash
# 在 Git Bash 中运行
./tests/curl_tests.sh
```

## 生产环境部署

### 1. 使用 Gunicorn (推荐)

#### 1.1 安装 Gunicorn

```cmd
pip install gunicorn
```

#### 1.2 创建 Gunicorn 配置文件

创建 `gunicorn.conf.py`：

```python
# gunicorn.conf.py
bind = "0.0.0.0:5000"
workers = 4
worker_class = "sync"
worker_connections = 1000
timeout = 30
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
preload_app = True
```

#### 1.3 运行 Gunicorn

```cmd
gunicorn -c gunicorn.conf.py src.main:app
```

### 2. 使用 Waitress (Windows 推荐)

#### 2.1 安装 Waitress

```cmd
pip install waitress
```

#### 2.2 运行 Waitress

```cmd
waitress-serve --host=0.0.0.0 --port=5000 src.main:app
```

### 3. 配置反向代理 (可选)

#### 3.1 安装 Nginx for Windows

1. 下载 Nginx: http://nginx.org/en/download.html
2. 解压到 `C:\nginx`

#### 3.2 配置 Nginx

编辑 `C:\nginx\conf\nginx.conf`：

```nginx
server {
    listen 80;
    server_name localhost;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

#### 3.3 启动 Nginx

```cmd
cd C:\nginx
start nginx
```

### 4. 创建 Windows 服务 (可选)

#### 4.1 安装 NSSM

1. 下载 NSSM: https://nssm.cc/download
2. 解压并将 nssm.exe 添加到 PATH

#### 4.2 创建服务

```cmd
nssm install AIAppStore
```

在弹出的窗口中配置：
- Path: `C:\Projects\ai_app_store\venv\Scripts\python.exe`
- Startup directory: `C:\Projects\ai_app_store`
- Arguments: `src\main.py`

#### 4.3 启动服务

```cmd
nssm start AIAppStore
```

## 常见问题

### 1. Python 相关问题

#### Q: 提示 "python 不是内部或外部命令"
**A**: Python 未正确添加到 PATH 环境变量。重新安装 Python 并确保勾选 "Add Python to PATH"。

#### Q: pip 安装包时网络超时
**A**: 使用国内镜像源：
```cmd
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

#### Q: 虚拟环境激活失败
**A**: 
- 确保在正确的目录下
- 使用正确的激活命令
- 在 PowerShell 中可能需要修改执行策略

### 2. 数据库相关问题

#### Q: SQLite 数据库文件权限错误
**A**: 确保应用有写入权限，或将项目移动到用户目录下。

#### Q: PostgreSQL 连接失败
**A**: 
- 检查 PostgreSQL 服务是否运行
- 验证连接字符串中的用户名、密码、主机和端口
- 确保防火墙允许连接

### 3. 应用运行问题

#### Q: 端口 5000 被占用
**A**: 修改 `src/main.py` 中的端口号：
```python
app.run(host='0.0.0.0', port=8000, debug=True)
```

#### Q: CORS 错误
**A**: 确保已安装并配置 flask-cors：
```python
from flask_cors import CORS
CORS(app)
```

### 4. 性能问题

#### Q: 应用响应慢
**A**: 
- 使用生产级 WSGI 服务器 (Gunicorn/Waitress)
- 配置数据库连接池
- 启用缓存
- 优化数据库查询

## 性能优化

### 1. 数据库优化

#### 1.1 添加数据库索引

```python
# 在模型中添加索引
class App(db.Model):
    # ... 其他字段
    name = db.Column(db.String(200), nullable=False, index=True)
    status = db.Column(db.String(20), default='active', index=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
```

#### 1.2 配置连接池

```python
# 在 main.py 中添加
app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
    'pool_size': 10,
    'pool_recycle': 120,
    'pool_pre_ping': True
}
```

### 2. 缓存配置

#### 2.1 安装 Redis (可选)

1. 下载 Redis for Windows: https://github.com/microsoftarchive/redis/releases
2. 安装并启动 Redis 服务

#### 2.2 配置 Flask-Caching

```cmd
pip install Flask-Caching redis
```

```python
from flask_caching import Cache

cache = Cache(app, config={'CACHE_TYPE': 'redis'})

@cache.cached(timeout=300)
def get_apps():
    # 缓存应用列表
    pass
```

### 3. 静态文件优化

#### 3.1 配置静态文件缓存

在 Nginx 配置中添加：

```nginx
location /static/ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

#### 3.2 启用 Gzip 压缩

```nginx
gzip on;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml;
```

### 4. 监控和日志

#### 4.1 配置日志

```python
import logging
from logging.handlers import RotatingFileHandler

if not app.debug:
    file_handler = RotatingFileHandler('logs/app.log', maxBytes=10240, backupCount=10)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
```

#### 4.2 性能监控

安装性能监控工具：

```cmd
pip install flask-profiler
```

## 安全建议

### 1. 环境变量配置

创建 `.env` 文件存储敏感信息：

```env
SECRET_KEY=your-secret-key-here
DATABASE_URL=postgresql://user:password@localhost/dbname
DEBUG=False
```

### 2. 防火墙配置

在 Windows 防火墙中：
1. 打开 "Windows Defender 防火墙"
2. 点击 "高级设置"
3. 创建入站规则允许端口 5000 (或你使用的端口)

### 3. HTTPS 配置

在生产环境中使用 HTTPS：

```python
# 强制 HTTPS
@app.before_request
def force_https():
    if not request.is_secure and app.env != 'development':
        return redirect(request.url.replace('http://', 'https://'))
```

## 备份和恢复

### 1. 数据库备份

#### SQLite 备份
```cmd
copy src\database\app.db backup\app_backup_%date%.db
```

#### PostgreSQL 备份
```cmd
pg_dump -U app_user -h localhost ai_app_store > backup\app_backup_%date%.sql
```

### 2. 应用备份

创建完整的应用备份：

```cmd
# 创建备份目录
mkdir backup\%date%

# 复制应用文件
xcopy /E /I ai_app_store backup\%date%\ai_app_store

# 导出依赖列表
pip freeze > backup\%date%\requirements.txt
```

## 更新和维护

### 1. 应用更新

```cmd
# 停止应用
# 备份当前版本
# 更新代码
git pull origin main

# 更新依赖
pip install -r requirements.txt

# 运行数据库迁移 (如果有)
# 重启应用
```

### 2. 依赖更新

```cmd
# 检查过时的包
pip list --outdated

# 更新特定包
pip install --upgrade package_name

# 更新所有包 (谨慎使用)
pip freeze > requirements_old.txt
pip install --upgrade -r requirements.txt
```

## 总结

本文档提供了在 Windows 11 系统上部署 AI App Store 后台系统的完整指南。通过遵循这些步骤，你可以成功地在开发和生产环境中运行应用。

关键要点：
- 确保 Python 和 Git 正确安装并添加到 PATH
- 使用虚拟环境隔离项目依赖
- 在生产环境中使用专业的 WSGI 服务器
- 定期备份数据和应用代码
- 监控应用性能和安全性

如果遇到问题，请参考常见问题部分或联系技术支持。

