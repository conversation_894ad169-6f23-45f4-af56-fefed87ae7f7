#!/bin/bash

# AI App Store API Curl 测试脚本
# 使用方法: ./curl_tests.sh [BASE_URL]
# 默认 BASE_URL: http://localhost:5000

BASE_URL=${1:-"http://localhost:5000"}
API_URL="$BASE_URL/api"

echo "=== AI App Store API 测试 ==="
echo "API 基础 URL: $API_URL"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo -e "${YELLOW}测试: $description${NC}"
    echo "请求: $method $API_URL$endpoint"
    
    if [ -n "$data" ]; then
        echo "数据: $data"
        response=$(curl -s -w "\n%{http_code}" -X $method \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$API_URL$endpoint")
    else
        response=$(curl -s -w "\n%{http_code}" -X $method "$API_URL$endpoint")
    fi
    
    # 分离响应体和状态码
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)
    
    echo "状态码: $http_code"
    echo "响应: $response_body"
    
    if [[ $http_code -ge 200 && $http_code -lt 300 ]]; then
        echo -e "${GREEN}✓ 测试通过${NC}"
    else
        echo -e "${RED}✗ 测试失败${NC}"
    fi
    echo "----------------------------------------"
}

echo "1. 用户 API 测试"
echo "========================================"

# 创建用户
test_api "POST" "/users" '{
    "username": "testuser1",
    "email": "<EMAIL>",
    "full_name": "Test User 1",
    "bio": "AI开发者",
    "github_username": "testuser1",
    "location": "北京"
}' "创建用户1"

test_api "POST" "/users" '{
    "username": "testuser2",
    "email": "<EMAIL>",
    "full_name": "Test User 2",
    "bio": "前端开发者"
}' "创建用户2"

# 获取用户列表
test_api "GET" "/users" "" "获取用户列表"

# 获取单个用户
test_api "GET" "/users/1" "" "获取用户详情"

# 更新用户
test_api "PUT" "/users/1" '{
    "bio": "更新后的个人简介",
    "location": "上海"
}' "更新用户信息"

echo ""
echo "2. 分类 API 测试"
echo "========================================"

# 创建分类
test_api "POST" "/categories" '{
    "name": "AI工具",
    "description": "人工智能相关的应用工具"
}' "创建分类1"

test_api "POST" "/categories" '{
    "name": "Web应用",
    "description": "网页应用程序"
}' "创建分类2"

# 获取分类列表
test_api "GET" "/categories" "" "获取分类列表"

# 获取单个分类
test_api "GET" "/categories/1" "" "获取分类详情"

echo ""
echo "3. 应用 API 测试"
echo "========================================"

# 创建应用
test_api "POST" "/apps" '{
    "name": "AI聊天助手",
    "description": "基于GPT的智能聊天助手，支持多种对话模式和个性化设置。",
    "short_description": "智能聊天助手",
    "vercel_url": "https://ai-chat-assistant.vercel.app",
    "github_url": "https://github.com/testuser1/ai-chat-assistant",
    "icon_url": "https://example.com/icon1.png",
    "screenshots": ["https://example.com/screenshot1.png", "https://example.com/screenshot2.png"],
    "tags": ["AI", "聊天", "GPT"],
    "ai_framework": "OpenAI GPT-4",
    "tech_stack": ["React", "Next.js", "Python", "FastAPI"],
    "user_id": 1,
    "category_id": 1
}' "创建应用1"

test_api "POST" "/apps" '{
    "name": "代码生成器",
    "description": "自动生成高质量代码的AI工具，支持多种编程语言。",
    "short_description": "AI代码生成工具",
    "vercel_url": "https://code-generator.vercel.app",
    "github_url": "https://github.com/testuser2/code-generator",
    "tags": ["AI", "代码", "生成器"],
    "ai_framework": "Codex",
    "tech_stack": ["Vue.js", "Node.js"],
    "user_id": 2,
    "category_id": 1,
    "featured": true
}' "创建应用2"

# 获取应用列表
test_api "GET" "/apps" "" "获取应用列表"

# 获取推荐应用
test_api "GET" "/apps?featured=true" "" "获取推荐应用"

# 搜索应用
test_api "GET" "/apps?search=AI" "" "搜索包含AI的应用"

# 按分类筛选
test_api "GET" "/apps?category_id=1" "" "获取分类1的应用"

# 获取单个应用
test_api "GET" "/apps/1" "" "获取应用详情"

# 更新应用
test_api "PUT" "/apps/1" '{
    "description": "更新后的应用描述",
    "featured": true
}' "更新应用信息"

echo ""
echo "4. 评论 API 测试"
echo "========================================"

# 创建评论
test_api "POST" "/apps/1/reviews" '{
    "rating": 5,
    "comment": "非常棒的应用！界面友好，功能强大。",
    "user_id": 2
}' "创建应用评论"

test_api "POST" "/apps/2/reviews" '{
    "rating": 4,
    "comment": "很实用的工具，节省了很多开发时间。",
    "user_id": 1
}' "创建另一个评论"

# 获取应用评论
test_api "GET" "/apps/1/reviews" "" "获取应用1的评论"

echo ""
echo "5. 点赞 API 测试"
echo "========================================"

# 点赞应用
test_api "POST" "/apps/1/like" '{
    "user_id": 2
}' "用户2点赞应用1"

test_api "POST" "/apps/2/like" '{
    "user_id": 1
}' "用户1点赞应用2"

# 取消点赞
test_api "POST" "/apps/1/like" '{
    "user_id": 2
}' "用户2取消点赞应用1"

echo ""
echo "6. 统计 API 测试"
echo "========================================"

# 获取应用统计
test_api "GET" "/apps/stats" "" "获取应用统计信息"

echo ""
echo "7. 用户相关 API 测试"
echo "========================================"

# 获取用户的应用
test_api "GET" "/users/1/apps" "" "获取用户1的应用"

# 获取用户的评论
test_api "GET" "/users/1/reviews" "" "获取用户1的评论"

# 获取用户点赞的应用
test_api "GET" "/users/1/likes" "" "获取用户1点赞的应用"

echo ""
echo "8. 分类相关 API 测试"
echo "========================================"

# 获取分类下的应用
test_api "GET" "/categories/1/apps" "" "获取分类1下的应用"

echo ""
echo "=== 测试完成 ==="
echo "注意: 某些测试可能因为数据依赖而失败，这是正常的。"
echo "建议按顺序运行测试，或者在每次测试前重置数据库。"

