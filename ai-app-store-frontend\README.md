# AI App Store Frontend

一个现代化的 AI 应用商店前端界面，使用 React + Vite 构建，部署在 Vercel 平台。

## 🌐 在线访问

**部署地址**: https://qmjijscy.manus.space

## 🚀 功能特性

### 核心功能
- **应用展示**: 展示所有 AI 应用的卡片式布局
- **开发者列表**: 查看所有注册开发者信息
- **分类管理**: 按分类浏览应用
- **搜索功能**: 实时搜索应用和开发者
- **统计面板**: 显示平台统计数据

### 用户交互
- **点赞功能**: 为喜欢的应用点赞
- **外部链接**: 直接访问应用的 Vercel 部署地址
- **GitHub 集成**: 查看应用源码
- **响应式设计**: 支持桌面和移动设备

## 🛠️ 技术栈

- **框架**: React 18 + Vite 6
- **UI 库**: shadcn/ui + Tailwind CSS
- **图标**: Lucide React
- **状态管理**: React Hooks
- **HTTP 客户端**: Fetch API
- **部署平台**: Vercel

## 🔌 API 对接

前端应用对接后台 Flask API：

**API 基础地址**: `https://9000-ixncvei4umbd6nkzcd888-831d6787.manusvm.computer/api`

### 主要 API 端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/users` | GET | 获取用户列表 |
| `/apps` | GET | 获取应用列表 |
| `/categories` | GET | 获取分类列表 |
| `/apps/stats` | GET | 获取统计数据 |
| `/apps/{id}/like` | POST | 点赞应用 |

## 📁 项目结构

```
ai-app-store-frontend/
├── public/                 # 静态资源
├── src/
│   ├── components/
│   │   └── ui/            # shadcn/ui 组件
│   ├── App.jsx            # 主应用组件
│   ├── App.css            # 应用样式
│   ├── index.css          # 全局样式
│   └── main.jsx           # 入口文件
├── .env                   # 环境变量
├── vercel.json            # Vercel 配置
├── package.json           # 项目依赖
└── README.md              # 项目说明
```

## 🚀 本地开发

### 环境要求
- Node.js 18+
- pnpm (推荐) 或 npm

### 安装依赖
```bash
pnpm install
```

### 启动开发服务器
```bash
pnpm run dev
```

访问 http://localhost:5173

### 构建生产版本
```bash
pnpm run build
```

## 🌍 部署到 Vercel

### 自动部署
1. 将代码推送到 GitHub 仓库
2. 在 Vercel 控制台导入项目
3. 配置环境变量 `VITE_API_BASE_URL`
4. 自动部署完成

### 手动部署
```bash
# 安装 Vercel CLI
npm install -g vercel

# 登录 Vercel
vercel login

# 部署项目
vercel --prod
```

## ⚙️ 环境变量

创建 `.env` 文件：

```env
VITE_API_BASE_URL=https://9000-ixncvei4umbd6nkzcd888-831d6787.manusvm.computer/api
```

## 🎨 界面预览

### 主页面
- 顶部导航栏包含搜索功能
- 统计卡片显示平台数据
- 标签页切换不同内容视图

### 应用列表
- 卡片式布局展示应用
- 显示应用图标、名称、描述
- 包含评分、点赞数、浏览量
- 支持分类筛选和搜索

### 开发者页面
- 展示开发者头像和信息
- 显示开发者的应用数量
- 支持跳转到 GitHub 主页

### 分类页面
- 显示所有应用分类
- 统计每个分类的应用数量

## 🔧 自定义配置

### 修改 API 地址
在 `.env` 文件中修改 `VITE_API_BASE_URL`

### 添加新功能
1. 在 `src/App.jsx` 中添加新的状态和函数
2. 创建新的 UI 组件
3. 更新 API 调用逻辑

### 样式定制
- 修改 `src/App.css` 自定义样式
- 使用 Tailwind CSS 类名调整布局
- 通过 shadcn/ui 主题定制组件外观

## 📱 响应式设计

- **桌面端**: 完整的多列布局
- **平板端**: 自适应的两列布局
- **移动端**: 单列堆叠布局
- **触摸优化**: 适合移动设备操作

## 🔒 安全特性

- **CORS 支持**: 安全的跨域请求
- **XSS 防护**: 输入内容转义
- **安全头**: 通过 Vercel 配置安全响应头
- **HTTPS**: 强制使用 HTTPS 连接

## 🚀 性能优化

- **代码分割**: 按需加载组件
- **图片优化**: 使用现代图片格式
- **缓存策略**: 合理的缓存配置
- **压缩**: Gzip 压缩静态资源

## 🐛 故障排除

### API 连接问题
1. 检查 API 服务器是否运行
2. 验证 API 地址是否正确
3. 查看浏览器控制台错误信息

### 部署问题
1. 确认环境变量配置正确
2. 检查构建日志错误信息
3. 验证 Vercel 配置文件

### 样式问题
1. 清除浏览器缓存
2. 检查 Tailwind CSS 配置
3. 验证组件导入路径

## 📄 许可证

MIT License

## 👥 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 联系方式

- **开发团队**: Manus AI
- **项目地址**: https://qmjijscy.manus.space
- **API 文档**: https://9000-ixncvei4umbd6nkzcd888-831d6787.manusvm.computer

---

**由 Manus AI 构建** 🤖 | **部署在 Vercel** ⚡

