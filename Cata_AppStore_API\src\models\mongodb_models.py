from pymongo import MongoClient
from datetime import datetime
from bson import ObjectId
import os
from dotenv import load_dotenv

load_dotenv()

class MongoDB:
    def __init__(self):
        self.client = MongoClient(os.getenv('MONGODB_URI', 'mongodb://localhost:27017/'))
        self.db = self.client[os.getenv('MONGODB_DB', 'appstore')]
        
    def get_users_collection(self):
        return self.db.users
        
    def get_apps_collection(self):
        return self.db.apps
        
    def get_categories_collection(self):
        return self.db.categories

mongodb = MongoDB()

class User:
    def __init__(self, data):
        self.data = data
        
    @staticmethod
    def create_user(username, email, password, full_name=None, role='user'):
        users_collection = mongodb.get_users_collection()
        
        # 检查用户是否已存在
        if users_collection.find_one({'$or': [{'username': username}, {'email': email}]}):
            return None
            
        user_data = {
            'username': username,
            'email': email,
            'password': password,  # 在实际应用中应该加密
            'full_name': full_name,
            'role': role,  # 'user', 'admin'
            'bio': '',
            'avatar_url': '',
            'website_url': '',
            'location': '',
            'is_verified': False,
            'is_active': True,
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        }
        
        result = users_collection.insert_one(user_data)
        user_data['_id'] = result.inserted_id
        return user_data
        
    @staticmethod
    def find_by_username(username):
        users_collection = mongodb.get_users_collection()
        return users_collection.find_one({'username': username})
        
    @staticmethod
    def find_by_email(email):
        users_collection = mongodb.get_users_collection()
        return users_collection.find_one({'email': email})
        
    @staticmethod
    def find_by_id(user_id):
        users_collection = mongodb.get_users_collection()
        try:
            return users_collection.find_one({'_id': ObjectId(user_id)})
        except:
            return None
            
    @staticmethod
    def update_user(user_id, update_data):
        users_collection = mongodb.get_users_collection()
        update_data['updated_at'] = datetime.utcnow()
        try:
            result = users_collection.update_one(
                {'_id': ObjectId(user_id)},
                {'$set': update_data}
            )
            return result.modified_count > 0
        except:
            return False

class AppStore:
    def __init__(self, data):
        self.data = data
        
    @staticmethod
    def create_app(title, url, zip_file_path, user_id, description='', category_id=None, tags=None):
        apps_collection = mongodb.get_apps_collection()
        
        app_data = {
            'title': title,
            'url': url,
            'zip_file_path': zip_file_path,
            'extracted_path': '',  # 解压后的路径
            'description': description,
            'category_id': ObjectId(category_id) if category_id else None,
            'tags': tags or [],
            'user_id': ObjectId(user_id),
            'status': 'pending',  # pending, approved, rejected
            'featured': False,
            'download_count': 0,
            'view_count': 0,
            'like_count': 0,
            'screenshots': [],
            'version': '1.0.0',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        }
        
        result = apps_collection.insert_one(app_data)
        app_data['_id'] = result.inserted_id
        return app_data
        
    @staticmethod
    def get_all_apps(status='approved', limit=50):
        apps_collection = mongodb.get_apps_collection()
        query = {}
        if status:
            query['status'] = status
            
        apps = list(apps_collection.find(query).sort('created_at', -1).limit(limit))
        return apps
        
    @staticmethod
    def get_app_by_id(app_id):
        apps_collection = mongodb.get_apps_collection()
        try:
            app = apps_collection.find_one({'_id': ObjectId(app_id)})
            return app
        except:
            return None
            
    @staticmethod
    def get_apps_by_user(user_id, status=None):
        apps_collection = mongodb.get_apps_collection()
        query = {'user_id': ObjectId(user_id)}
        if status:
            query['status'] = status
            
        apps = list(apps_collection.find(query).sort('created_at', -1))
        return apps
        
    @staticmethod
    def update_app(app_id, update_data):
        apps_collection = mongodb.get_apps_collection()
        update_data['updated_at'] = datetime.utcnow()
        
        # 处理ObjectId字段
        if 'category_id' in update_data and update_data['category_id']:
            update_data['category_id'] = ObjectId(update_data['category_id'])
        if 'user_id' in update_data and update_data['user_id']:
            update_data['user_id'] = ObjectId(update_data['user_id'])
            
        try:
            result = apps_collection.update_one(
                {'_id': ObjectId(app_id)},
                {'$set': update_data}
            )
            return result.modified_count > 0
        except:
            return False
            
    @staticmethod
    def delete_app(app_id):
        apps_collection = mongodb.get_apps_collection()
        try:
            result = apps_collection.delete_one({'_id': ObjectId(app_id)})
            return result.deleted_count > 0
        except:
            return False

class Category:
    def __init__(self, data):
        self.data = data
        
    @staticmethod
    def create_category(name, description=''):
        categories_collection = mongodb.get_categories_collection()
        
        # 检查分类是否已存在
        if categories_collection.find_one({'name': name}):
            return None
            
        category_data = {
            'name': name,
            'description': description,
            'created_at': datetime.utcnow()
        }
        
        result = categories_collection.insert_one(category_data)
        category_data['_id'] = result.inserted_id
        return category_data
        
    @staticmethod
    def get_all_categories():
        categories_collection = mongodb.get_categories_collection()
        return list(categories_collection.find().sort('name', 1))
        
    @staticmethod
    def get_category_by_id(category_id):
        categories_collection = mongodb.get_categories_collection()
        try:
            return categories_collection.find_one({'_id': ObjectId(category_id)})
        except:
            return None