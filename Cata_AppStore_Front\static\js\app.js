// 应用主要逻辑
class AppStore {
    constructor() {
        this.currentPage = 'home';
        this.apps = [];
        this.categories = [];
        this.currentUser = null;
        this.init();
    }

    async init() {
        await this.checkAuth();
        await this.loadCategories();
        this.bindEvents();
        this.showPage('home');
        this.loadFeaturedApps();
    }

    // 检查用户认证状态
    async checkAuth() {
        this.currentUser = AuthAPI.getCurrentUser();
        this.updateAuthUI();
    }

    // 更新认证UI
    updateAuthUI() {
        const authButtons = document.getElementById('auth-buttons');
        const userInfo = document.getElementById('user-info');
        const username = document.getElementById('username');
        const adminBtn = document.getElementById('admin-btn');

        if (this.currentUser) {
            authButtons.classList.add('d-none');
            userInfo.classList.remove('d-none');
            username.textContent = this.currentUser.username;
            
            // 如果是管理员，显示管理按钮
            if (this.currentUser.role === 'admin') {
                adminBtn.style.display = 'block';
            }
        } else {
            authButtons.classList.remove('d-none');
            userInfo.classList.add('d-none');
        }
    }

    // 加载分类
    async loadCategories() {
        try {
            const response = await CategoryAPI.getCategories();
            this.categories = response.categories;
            this.updateCategoryFilters();
        } catch (error) {
            this.showToast('加载分类失败', 'error');
        }
    }

    // 更新分类筛选器
    updateCategoryFilters() {
        const filters = document.querySelectorAll('#category-filter, #app-category');
        filters.forEach(filter => {
            filter.innerHTML = '<option value="">全部分类</option>';
            this.categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category._id;
                option.textContent = category.name;
                filter.appendChild(option);
            });
        });
    }

    // 绑定事件
    bindEvents() {
        // 登录表单
        document.getElementById('login-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.handleLogin();
        });

        // 注册表单
        document.getElementById('register-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.handleRegister();
        });

        // 上传应用表单
        document.getElementById('upload-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.handleUpload();
        });
    }

    // 处理登录
    async handleLogin() {
        const username = document.getElementById('login-username').value;
        const password = document.getElementById('login-password').value;

        try {
            const response = await AuthAPI.login(username, password);
            this.currentUser = response.user;
            this.updateAuthUI();
            this.showPage('home');
            this.showToast('登录成功', 'success');
        } catch (error) {
            this.showToast(error.message, 'error');
        }
    }

    // 处理注册
    async handleRegister() {
        const userData = {
            username: document.getElementById('register-username').value,
            email: document.getElementById('register-email').value,
            full_name: document.getElementById('register-fullname').value,
            password: document.getElementById('register-password').value
        };

        try {
            const response = await AuthAPI.register(userData);
            this.showToast('注册成功，请登录', 'success');
            this.showPage('login');
        } catch (error) {
            this.showToast(error.message, 'error');
        }
    }

    // 处理上传
    async handleUpload() {
        const formData = new FormData();
        formData.append('title', document.getElementById('app-title').value);
        formData.append('url', document.getElementById('app-url').value);
        formData.append('description', document.getElementById('app-description').value);
        formData.append('category_id', document.getElementById('app-category').value);
        formData.append('tags', document.getElementById('app-tags').value);

        const zipFile = document.getElementById('app-zip').files[0];
        if (zipFile) {
            formData.append('zip_file', zipFile);
        }

        try {
            const response = await AppAPI.createApp(formData);
            this.showToast('应用上传成功，等待审核', 'success');
            this.showPage('my-apps');
            this.loadMyApps();
        } catch (error) {
            this.showToast(error.message, 'error');
        }
    }

    // 加载推荐应用
    async loadFeaturedApps() {
        try {
            const response = await AppAPI.getApps();
            this.apps = response.apps;
            this.renderFeaturedApps();
        } catch (error) {
            this.showToast('加载应用失败', 'error');
        }
    }

    // 渲染推荐应用
    renderFeaturedApps() {
        const container = document.getElementById('featured-apps');
        container.innerHTML = '';

        const featuredApps = this.apps.slice(0, 6);
        featuredApps.forEach(app => {
            const appCard = this.createAppCard(app);
            container.appendChild(appCard);
        });
    }

    // 创建应用卡片
    createAppCard(app) {
        const col = document.createElement('div');
        col.className = 'col-md-4 mb-4';
        col.innerHTML = `
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title">${app.title}</h5>
                    <p class="card-text">${app.description || '暂无描述'}</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">${this.formatDate(app.created_at)}</small>
                        <div>
                            <span class="badge bg-primary">${app.download_count || 0} 下载</span>
                            <span class="badge bg-secondary">${app.view_count || 0} 浏览</span>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <button class="btn btn-primary btn-sm" onclick="appStore.viewApp('${app._id}')">
                        查看详情
                    </button>
                    <button class="btn btn-outline-primary btn-sm" onclick="appStore.downloadApp('${app._id}')">
                        下载
                    </button>
                </div>
            </div>
        `;
        return col;
    }

    // 查看应用详情
    async viewApp(appId) {
        try {
            const response = await AppAPI.getApp(appId);
            const app = response.app;
            this.showAppModal(app);
        } catch (error) {
            this.showToast('加载应用详情失败', 'error');
        }
    }

    // 下载应用
    async downloadApp(appId) {
        try {
            const response = await AppAPI.downloadApp(appId);
            if (response.redirect_url) {
                window.open(response.redirect_url, '_blank');
            }
        } catch (error) {
            this.showToast('下载失败', 'error');
        }
    }

    // 显示应用详情模态框
    showAppModal(app) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${app.title}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p><strong>描述:</strong> ${app.description || '暂无描述'}</p>
                        <p><strong>URL:</strong> <a href="${app.url}" target="_blank">${app.url}</a></p>
                        <p><strong>上传者:</strong> ${app.user_id}</p>
                        <p><strong>创建时间:</strong> ${this.formatDate(app.created_at)}</p>
                        <p><strong>下载次数:</strong> ${app.download_count || 0}</p>
                        <p><strong>浏览次数:</strong> ${app.view_count || 0}</p>
                        ${app.tags && app.tags.length > 0 ? `
                            <p><strong>标签:</strong> ${app.tags.map(tag => `<span class="badge bg-secondary">${tag}</span>`).join(' ')}</p>
                        ` : ''}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        <button type="button" class="btn btn-primary" onclick="window.open('${app.url}', '_blank')">访问应用</button>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    }

    // 加载我的应用
    async loadMyApps() {
        try {
            const response = await AppAPI.getMyApps();
            this.renderMyApps(response.apps);
        } catch (error) {
            this.showToast('加载我的应用失败', 'error');
        }
    }

    // 渲染我的应用
    renderMyApps(apps) {
        const container = document.getElementById('my-apps-list');
        container.innerHTML = '';

        if (apps.length === 0) {
            container.innerHTML = '<p class="text-muted">暂无应用</p>';
            return;
        }

        apps.forEach(app => {
            const appCard = this.createMyAppCard(app);
            container.appendChild(appCard);
        });
    }

    // 创建我的应用卡片
    createMyAppCard(app) {
        const col = document.createElement('div');
        col.className = 'col-md-4 mb-4';
        col.innerHTML = `
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title">${app.title}</h5>
                    <p class="card-text">${app.description || '暂无描述'}</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="badge bg-${this.getStatusColor(app.status)}">${this.getStatusText(app.status)}</span>
                        <small class="text-muted">${this.formatDate(app.created_at)}</small>
                    </div>
                </div>
                <div class="card-footer">
                    <button class="btn btn-primary btn-sm" onclick="appStore.viewApp('${app._id}')">
                        查看详情
                    </button>
                    <button class="btn btn-outline-danger btn-sm" onclick="appStore.deleteApp('${app._id}')">
                        删除
                    </button>
                </div>
            </div>
        `;
        return col;
    }

    // 删除应用
    async deleteApp(appId) {
        if (!confirm('确定要删除这个应用吗？')) {
            return;
        }

        try {
            await AppAPI.deleteApp(appId);
            this.showToast('应用删除成功', 'success');
            this.loadMyApps();
        } catch (error) {
            this.showToast('删除失败', 'error');
        }
    }

    // 加载管理员面板
    async loadAdminPanel() {
        try {
            const response = await AdminAPI.getDashboard();
            this.renderAdminDashboard(response.stats);
            this.loadPendingApps();
        } catch (error) {
            this.showToast('加载管理面板失败', 'error');
        }
    }

    // 渲染管理员仪表板
    renderAdminDashboard(stats) {
        document.getElementById('total-users').textContent = stats.total_users;
        document.getElementById('total-apps').textContent = stats.total_apps;
        document.getElementById('pending-apps').textContent = stats.pending_apps;
        document.getElementById('total-categories').textContent = stats.total_categories;
    }

    // 加载待审核应用
    async loadPendingApps() {
        try {
            const response = await AdminAPI.getAllApps({ status: 'pending' });
            this.renderPendingApps(response.apps);
        } catch (error) {
            this.showToast('加载待审核应用失败', 'error');
        }
    }

    // 渲染待审核应用
    renderPendingApps(apps) {
        const container = document.getElementById('pending-apps-list');
        container.innerHTML = '';

        if (apps.length === 0) {
            container.innerHTML = '<p class="text-muted">暂无待审核应用</p>';
            return;
        }

        apps.forEach(app => {
            const appCard = this.createPendingAppCard(app);
            container.appendChild(appCard);
        });
    }

    // 创建待审核应用卡片
    createPendingAppCard(app) {
        const col = document.createElement('div');
        col.className = 'col-md-6 mb-4';
        col.innerHTML = `
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">${app.title}</h5>
                    <p class="card-text">${app.description || '暂无描述'}</p>
                    <p class="card-text"><small class="text-muted">上传者: ${app.user_id}</small></p>
                </div>
                <div class="card-footer">
                    <button class="btn btn-success btn-sm" onclick="appStore.approveApp('${app._id}')">
                        通过
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="appStore.rejectApp('${app._id}')">
                        拒绝
                    </button>
                    <button class="btn btn-primary btn-sm" onclick="appStore.viewApp('${app._id}')">
                        查看详情
                    </button>
                </div>
            </div>
        `;
        return col;
    }

    // 通过应用
    async approveApp(appId) {
        try {
            await AdminAPI.approveApp(appId);
            this.showToast('应用已通过审核', 'success');
            this.loadAdminPanel();
        } catch (error) {
            this.showToast('审核失败', 'error');
        }
    }

    // 拒绝应用
    async rejectApp(appId) {
        const reason = prompt('请输入拒绝原因：');
        if (!reason) return;

        try {
            await AdminAPI.rejectApp(appId, reason);
            this.showToast('应用已拒绝', 'success');
            this.loadAdminPanel();
        } catch (error) {
            this.showToast('拒绝失败', 'error');
        }
    }

    // 工具函数
    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('zh-CN');
    }

    getStatusColor(status) {
        const colors = {
            'pending': 'warning',
            'approved': 'success',
            'rejected': 'danger'
        };
        return colors[status] || 'secondary';
    }

    getStatusText(status) {
        const texts = {
            'pending': '待审核',
            'approved': '已通过',
            'rejected': '已拒绝'
        };
        return texts[status] || '未知';
    }

    showToast(message, type = 'info') {
        const toast = document.getElementById('toast');
        const toastMessage = document.getElementById('toast-message');
        const toastHeader = toast.querySelector('.toast-header');
        
        toastMessage.textContent = message;
        
        // 设置颜色
        toastHeader.className = `toast-header bg-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'primary'} text-white`;
        
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
    }

    // 页面导航
    showPage(pageName) {
        // 隐藏所有页面
        document.querySelectorAll('.page').forEach(page => {
            page.classList.add('d-none');
        });
        
        // 显示目标页面
        const targetPage = document.getElementById(`${pageName}-page`);
        if (targetPage) {
            targetPage.classList.remove('d-none');
        }
        
        this.currentPage = pageName;
    }
}

// 全局函数
function showHome() {
    appStore.showPage('home');
}

function showLogin() {
    appStore.showPage('login');
}

function showRegister() {
    appStore.showPage('register');
}

function showApps() {
    appStore.showPage('apps');
}

function showUpload() {
    appStore.showPage('upload');
}

function showMyApps() {
    appStore.showPage('my-apps');
    appStore.loadMyApps();
}

function showAdmin() {
    appStore.showPage('admin');
    appStore.loadAdminPanel();
}

function showCategories() {
    appStore.showPage('apps');
}

function logout() {
    AuthAPI.logout();
    appStore.currentUser = null;
    appStore.updateAuthUI();
    appStore.showPage('home');
    appStore.showToast('已退出登录', 'success');
}

// 启动应用
const appStore = new AppStore();