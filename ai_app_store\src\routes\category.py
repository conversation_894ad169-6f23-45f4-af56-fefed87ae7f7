from flask import Blueprint, request, jsonify
from src.models.user import db
from src.models.app import Category, App
from datetime import datetime

category_bp = Blueprint('category', __name__)

@category_bp.route('/categories', methods=['GET'])
def get_categories():
    """获取分类列表"""
    try:
        include_stats = request.args.get('include_stats', False, type=bool)
        
        categories = Category.query.order_by(Category.name).all()
        
        return jsonify({
            'success': True,
            'data': [category.to_dict() for category in categories]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@category_bp.route('/categories/<int:category_id>', methods=['GET'])
def get_category(category_id):
    """获取单个分类详情"""
    try:
        category = Category.query.get_or_404(category_id)
        
        return jsonify({
            'success': True,
            'data': category.to_dict()
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@category_bp.route('/categories', methods=['POST'])
def create_category():
    """创建新分类"""
    try:
        data = request.get_json()
        
        # 验证必需字段
        if 'name' not in data:
            return jsonify({'success': False, 'error': 'Missing required field: name'}), 400
        
        # 检查分类名是否已存在
        existing_category = Category.query.filter_by(name=data['name']).first()
        if existing_category:
            return jsonify({'success': False, 'error': 'Category name already exists'}), 400
        
        category = Category(
            name=data['name'],
            description=data.get('description')
        )
        
        db.session.add(category)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'data': category.to_dict(),
            'message': 'Category created successfully'
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@category_bp.route('/categories/<int:category_id>', methods=['PUT'])
def update_category(category_id):
    """更新分类"""
    try:
        category = Category.query.get_or_404(category_id)
        data = request.get_json()
        
        # 更新字段
        if 'name' in data:
            # 检查新名称是否已存在（排除当前分类）
            existing_category = Category.query.filter(
                Category.name == data['name'],
                Category.id != category_id
            ).first()
            if existing_category:
                return jsonify({'success': False, 'error': 'Category name already exists'}), 400
            category.name = data['name']
        
        if 'description' in data:
            category.description = data['description']
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'data': category.to_dict(),
            'message': 'Category updated successfully'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@category_bp.route('/categories/<int:category_id>', methods=['DELETE'])
def delete_category(category_id):
    """删除分类"""
    try:
        category = Category.query.get_or_404(category_id)
        
        # 检查是否有应用使用此分类
        app_count = App.query.filter_by(category_id=category_id).count()
        if app_count > 0:
            return jsonify({
                'success': False, 
                'error': f'Cannot delete category. {app_count} apps are using this category.'
            }), 400
        
        db.session.delete(category)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Category deleted successfully'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@category_bp.route('/categories/<int:category_id>/apps', methods=['GET'])
def get_category_apps(category_id):
    """获取分类下的应用"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        category = Category.query.get_or_404(category_id)
        
        pagination = App.query.filter_by(
            category_id=category_id, 
            status='active'
        ).order_by(App.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        apps = [app.to_dict() for app in pagination.items]
        
        return jsonify({
            'success': True,
            'data': {
                'category': category.to_dict(),
                'apps': apps,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': pagination.total,
                    'pages': pagination.pages,
                    'has_next': pagination.has_next,
                    'has_prev': pagination.has_prev
                }
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

