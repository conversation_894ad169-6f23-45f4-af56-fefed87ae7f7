from flask import Blueprint, request, jsonify
from bson import ObjectId
from ..models.mongodb_models import User, AppStore, Category, mongodb
from ..utils.file_handler import FileHandler
from .app_routes import token_required, admin_required
import os

admin_bp = Blueprint('admin', __name__)
file_handler = FileHandler()

@admin_bp.route('/dashboard', methods=['GET'])
@token_required
@admin_required
def get_dashboard(current_user):
    try:
        users_collection = mongodb.get_users_collection()
        apps_collection = mongodb.get_apps_collection()
        categories_collection = mongodb.get_categories_collection()
        
        # 获取统计信息
        total_users = users_collection.count_documents({})
        total_apps = apps_collection.count_documents({})
        total_categories = categories_collection.count_documents({})
        pending_apps = apps_collection.count_documents({'status': 'pending'})
        approved_apps = apps_collection.count_documents({'status': 'approved'})
        
        # 获取最近的应用
        recent_apps = list(apps_collection.find({}).sort('created_at', -1).limit(10))
        for app in recent_apps:
            app['_id'] = str(app['_id'])
            if 'user_id' in app:
                app['user_id'] = str(app['user_id'])
            if 'category_id' in app and app['category_id']:
                app['category_id'] = str(app['category_id'])
                
        # 获取最近注册的用户
        recent_users = list(users_collection.find({}).sort('created_at', -1).limit(10))
        for user in recent_users:
            user['_id'] = str(user['_id'])
            user.pop('password', None)  # 移除密码字段
            
        return jsonify({
            'stats': {
                'total_users': total_users,
                'total_apps': total_apps,
                'total_categories': total_categories,
                'pending_apps': pending_apps,
                'approved_apps': approved_apps
            },
            'recent_apps': recent_apps,
            'recent_users': recent_users
        }), 200
        
    except Exception as e:
        return jsonify({'message': f'获取仪表板数据失败: {str(e)}'}), 500

@admin_bp.route('/apps', methods=['GET'])
@token_required
@admin_required
def get_all_apps_admin(current_user):
    try:
        status = request.args.get('status', 'all')
        limit = int(request.args.get('limit', 100))
        
        query = {}
        if status != 'all':
            query['status'] = status
            
        apps_collection = mongodb.get_apps_collection()
        apps = list(apps_collection.find(query).sort('created_at', -1).limit(limit))
        
        for app in apps:
            app['_id'] = str(app['_id'])
            if 'user_id' in app:
                app['user_id'] = str(app['user_id'])
            if 'category_id' in app and app['category_id']:
                app['category_id'] = str(app['category_id'])
                
        return jsonify({'apps': apps}), 200
        
    except Exception as e:
        return jsonify({'message': f'获取应用列表失败: {str(e)}'}), 500

@admin_bp.route('/apps/<app_id>/approve', methods=['POST'])
@token_required
@admin_required
def approve_app(current_user, app_id):
    try:
        if AppStore.update_app(app_id, {'status': 'approved'}):
            app = AppStore.get_app_by_id(app_id)
            app['_id'] = str(app['_id'])
            return jsonify({'message': '应用已通过审核', 'app': app}), 200
        else:
            return jsonify({'message': '审核失败'}), 500
            
    except Exception as e:
        return jsonify({'message': f'审核应用失败: {str(e)}'}), 500

@admin_bp.route('/apps/<app_id>/reject', methods=['POST'])
@token_required
@admin_required
def reject_app(current_user, app_id):
    try:
        data = request.get_json()
        reason = data.get('reason', '')
        
        if AppStore.update_app(app_id, {'status': 'rejected', 'rejection_reason': reason}):
            app = AppStore.get_app_by_id(app_id)
            app['_id'] = str(app['_id'])
            return jsonify({'message': '应用已拒绝', 'app': app}), 200
        else:
            return jsonify({'message': '拒绝失败'}), 500
            
    except Exception as e:
        return jsonify({'message': f'拒绝应用失败: {str(e)}'}), 500

@admin_bp.route('/apps/<app_id>/feature', methods=['POST'])
@token_required
@admin_required
def feature_app(current_user, app_id):
    try:
        data = request.get_json()
        featured = data.get('featured', True)
        
        if AppStore.update_app(app_id, {'featured': featured}):
            app = AppStore.get_app_by_id(app_id)
            app['_id'] = str(app['_id'])
            return jsonify({'message': f'应用已{"" if featured else "取消"}推荐', 'app': app}), 200
        else:
            return jsonify({'message': '设置推荐失败'}), 500
            
    except Exception as e:
        return jsonify({'message': f'设置推荐失败: {str(e)}'}), 500

@admin_bp.route('/users', methods=['GET'])
@token_required
@admin_required
def get_all_users(current_user):
    try:
        limit = int(request.args.get('limit', 100))
        
        users_collection = mongodb.get_users_collection()
        users = list(users_collection.find({}).sort('created_at', -1).limit(limit))
        
        for user in users:
            user['_id'] = str(user['_id'])
            user.pop('password', None)  # 移除密码字段
            
        return jsonify({'users': users}), 200
        
    except Exception as e:
        return jsonify({'message': f'获取用户列表失败: {str(e)}'}), 500

@admin_bp.route('/users/<user_id>', methods=['PUT'])
@token_required
@admin_required
def update_user(current_user, user_id):
    try:
        data = request.get_json()
        update_data = {}
        
        allowed_fields = ['full_name', 'bio', 'role', 'is_verified', 'is_active']
        for field in allowed_fields:
            if field in data:
                update_data[field] = data[field]
                
        if User.update_user(user_id, update_data):
            user = User.find_by_id(user_id)
            user['_id'] = str(user['_id'])
            user.pop('password', None)
            return jsonify({'message': '用户更新成功', 'user': user}), 200
        else:
            return jsonify({'message': '用户更新失败'}), 500
            
    except Exception as e:
        return jsonify({'message': f'更新用户失败: {str(e)}'}), 500

@admin_bp.route('/users/<user_id>', methods=['DELETE'])
@token_required
@admin_required
def delete_user(current_user, user_id):
    try:
        # 不能删除自己
        if str(current_user['_id']) == user_id:
            return jsonify({'message': '不能删除自己的账户'}), 400
            
        # 删除用户的所有应用
        apps = AppStore.get_apps_by_user(user_id)
        for app in apps:
            if app.get('zip_file_path'):
                file_handler.delete_file(app['zip_file_path'])
            if app.get('extracted_path'):
                file_handler.delete_file(app['extracted_path'])
            
        users_collection = mongodb.get_users_collection()
        result = users_collection.delete_one({'_id': ObjectId(user_id)})
        
        if result.deleted_count > 0:
            return jsonify({'message': '用户删除成功'}), 200
        else:
            return jsonify({'message': '用户不存在'}), 404
            
    except Exception as e:
        return jsonify({'message': f'删除用户失败: {str(e)}'}), 500

@admin_bp.route('/categories', methods=['POST'])
@token_required
@admin_required
def create_category(current_user):
    try:
        data = request.get_json()
        name = data.get('name')
        description = data.get('description', '')
        
        if not name:
            return jsonify({'message': '分类名称为必填项'}), 400
            
        category = Category.create_category(name, description)
        if category:
            category['_id'] = str(category['_id'])
            return jsonify({'message': '分类创建成功', 'category': category}), 201
        else:
            return jsonify({'message': '分类名称已存在'}), 400
            
    except Exception as e:
        return jsonify({'message': f'创建分类失败: {str(e)}'}), 500

@admin_bp.route('/categories/<category_id>', methods=['PUT'])
@token_required
@admin_required
def update_category(current_user, category_id):
    try:
        data = request.get_json()
        update_data = {}
        
        allowed_fields = ['name', 'description']
        for field in allowed_fields:
            if field in data:
                update_data[field] = data[field]
                
        categories_collection = mongodb.get_categories_collection()
        result = categories_collection.update_one(
            {'_id': ObjectId(category_id)},
            {'$set': update_data}
        )
        
        if result.modified_count > 0:
            category = Category.get_category_by_id(category_id)
            category['_id'] = str(category['_id'])
            return jsonify({'message': '分类更新成功', 'category': category}), 200
        else:
            return jsonify({'message': '分类更新失败'}), 500
            
    except Exception as e:
        return jsonify({'message': f'更新分类失败: {str(e)}'}), 500

@admin_bp.route('/categories/<category_id>', methods=['DELETE'])
@token_required
@admin_required
def delete_category(current_user, category_id):
    try:
        # 检查是否有应用使用此分类
        apps_collection = mongodb.get_apps_collection()
        apps_with_category = apps_collection.count_documents({'category_id': ObjectId(category_id)})
        
        if apps_with_category > 0:
            return jsonify({'message': '无法删除分类，还有应用使用此分类'}), 400
            
        categories_collection = mongodb.get_categories_collection()
        result = categories_collection.delete_one({'_id': ObjectId(category_id)})
        
        if result.deleted_count > 0:
            return jsonify({'message': '分类删除成功'}), 200
        else:
            return jsonify({'message': '分类不存在'}), 404
            
    except Exception as e:
        return jsonify({'message': f'删除分类失败: {str(e)}'}), 500

@admin_bp.route('/system/stats', methods=['GET'])
@token_required
@admin_required
def get_system_stats(current_user):
    try:
        users_collection = mongodb.get_users_collection()
        apps_collection = mongodb.get_apps_collection()
        categories_collection = mongodb.get_categories_collection()
        
        # 获取详细统计信息
        stats = {
            'users': {
                'total': users_collection.count_documents({}),
                'active': users_collection.count_documents({'is_active': True}),
                'verified': users_collection.count_documents({'is_verified': True}),
                'admins': users_collection.count_documents({'role': 'admin'})
            },
            'apps': {
                'total': apps_collection.count_documents({}),
                'pending': apps_collection.count_documents({'status': 'pending'}),
                'approved': apps_collection.count_documents({'status': 'approved'}),
                'rejected': apps_collection.count_documents({'status': 'rejected'}),
                'featured': apps_collection.count_documents({'featured': True})
            },
            'categories': {
                'total': categories_collection.count_documents({})
            }
        }
        
        # 获取最受欢迎的应用
        popular_apps = list(apps_collection.find({'status': 'approved'})
                            .sort('download_count', -1).limit(5))
        for app in popular_apps:
            app['_id'] = str(app['_id'])
            
        stats['popular_apps'] = popular_apps
        
        return jsonify({'stats': stats}), 200
        
    except Exception as e:
        return jsonify({'message': f'获取系统统计失败: {str(e)}'}), 500