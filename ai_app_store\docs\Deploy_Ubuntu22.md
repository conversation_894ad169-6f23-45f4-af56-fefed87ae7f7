# AI App Store Ubuntu 22.04 部署指南

**版本**: 1.0  
**作者**: Manus AI  
**创建日期**: 2025年7月24日  
**最后更新**: 2025年7月24日

## 目录

1. [系统要求](#系统要求)
2. [环境准备](#环境准备)
3. [项目部署](#项目部署)
4. [数据库配置](#数据库配置)
5. [运行应用](#运行应用)
6. [生产环境部署](#生产环境部署)
7. [系统服务配置](#系统服务配置)
8. [Nginx 反向代理](#nginx-反向代理)
9. [SSL/HTTPS 配置](#sslhttps-配置)
10. [监控和日志](#监控和日志)
11. [常见问题](#常见问题)
12. [性能优化](#性能优化)

## 系统要求

### 最低系统要求

- **操作系统**: Ubuntu 22.04 LTS (Jammy Jellyfish)
- **处理器**: 1 CPU 核心
- **内存**: 1GB RAM
- **存储空间**: 5GB 可用磁盘空间
- **网络**: 稳定的互联网连接

### 推荐系统配置

- **操作系统**: Ubuntu 22.04 LTS Server
- **处理器**: 2+ CPU 核心
- **内存**: 4GB+ RAM
- **存储空间**: 20GB+ 可用磁盘空间 (SSD 推荐)
- **网络**: 高速网络连接

## 环境准备

### 1. 系统更新

首先更新系统包：

```bash
sudo apt update && sudo apt upgrade -y
```

### 2. 安装基础工具

```bash
sudo apt install -y curl wget git vim unzip software-properties-common
```

### 3. 安装 Python 3.11

Ubuntu 22.04 默认包含 Python 3.10，我们需要安装 Python 3.11：

```bash
# 添加 deadsnakes PPA
sudo add-apt-repository ppa:deadsnakes/ppa -y
sudo apt update

# 安装 Python 3.11
sudo apt install -y python3.11 python3.11-venv python3.11-dev

# 安装 pip
curl -sS https://bootstrap.pypa.io/get-pip.py | python3.11

# 验证安装
python3.11 --version
python3.11 -m pip --version
```

### 4. 创建应用用户

为了安全起见，创建专门的应用用户：

```bash
sudo adduser --system --group --home /opt/ai_app_store appuser
sudo usermod -aG sudo appuser  # 如果需要 sudo 权限
```

### 5. 配置防火墙

```bash
# 启用 UFW
sudo ufw enable

# 允许 SSH
sudo ufw allow ssh

# 允许 HTTP 和 HTTPS
sudo ufw allow 80
sudo ufw allow 443

# 允许应用端口 (开发环境)
sudo ufw allow 5000

# 查看状态
sudo ufw status
```

## 项目部署

### 1. 切换到应用用户

```bash
sudo su - appuser
cd /opt/ai_app_store
```

### 2. 获取项目代码

#### 方法一：从 ZIP 文件

```bash
# 如果有 ZIP 文件
wget <zip-file-url> -O ai_app_store.zip
unzip ai_app_store.zip
mv ai_app_store-main ai_app_store  # 根据实际目录名调整
cd ai_app_store
```

#### 方法二：从 Git 仓库

```bash
git clone <repository-url> ai_app_store
cd ai_app_store
```

### 3. 创建虚拟环境

```bash
python3.11 -m venv venv
source venv/bin/activate
```

### 4. 安装项目依赖

```bash
# 升级 pip
pip install --upgrade pip

# 安装依赖
pip install -r requirements.txt

# 如果网络慢，使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 5. 设置环境变量

创建环境变量文件：

```bash
cat > .env << EOF
FLASK_APP=src/main.py
FLASK_ENV=production
SECRET_KEY=$(python3.11 -c 'import secrets; print(secrets.token_hex(32))')
DATABASE_URL=sqlite:///$(pwd)/src/database/app.db
EOF
```

### 6. 初始化数据库

```bash
# 确保数据库目录存在
mkdir -p src/database

# 运行应用初始化数据库
python3.11 src/main.py &
sleep 5
pkill -f "python3.11 src/main.py"
```

## 数据库配置

### 1. SQLite 配置 (开发/小型部署)

SQLite 是默认配置，无需额外设置。数据库文件位于：
```
/opt/ai_app_store/ai_app_store/src/database/app.db
```

### 2. PostgreSQL 配置 (推荐生产环境)

#### 2.1 安装 PostgreSQL

```bash
sudo apt install -y postgresql postgresql-contrib
```

#### 2.2 配置 PostgreSQL

```bash
# 切换到 postgres 用户
sudo -u postgres psql

# 在 PostgreSQL 命令行中执行
CREATE DATABASE ai_app_store;
CREATE USER appuser WITH PASSWORD 'secure_password_here';
GRANT ALL PRIVILEGES ON DATABASE ai_app_store TO appuser;
\q
```

#### 2.3 安装 Python PostgreSQL 驱动

```bash
# 激活虚拟环境
source venv/bin/activate

# 安装 psycopg2
pip install psycopg2-binary
```

#### 2.4 更新配置

修改 `.env` 文件：

```bash
cat > .env << EOF
FLASK_APP=src/main.py
FLASK_ENV=production
SECRET_KEY=$(python3.11 -c 'import secrets; print(secrets.token_hex(32))')
DATABASE_URL=postgresql://appuser:secure_password_here@localhost:5432/ai_app_store
EOF
```

修改 `src/main.py` 以使用环境变量：

```python
import os
from dotenv import load_dotenv

load_dotenv()

# 替换数据库配置
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL', 
    f"sqlite:///{os.path.join(os.path.dirname(__file__), 'database', 'app.db')}")
```

### 3. MySQL 配置 (可选)

#### 3.1 安装 MySQL

```bash
sudo apt install -y mysql-server
sudo mysql_secure_installation
```

#### 3.2 配置 MySQL

```bash
sudo mysql -u root -p

# 在 MySQL 命令行中执行
CREATE DATABASE ai_app_store CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'appuser'@'localhost' IDENTIFIED BY 'secure_password_here';
GRANT ALL PRIVILEGES ON ai_app_store.* TO 'appuser'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

#### 3.3 安装 Python MySQL 驱动

```bash
pip install PyMySQL
```

更新 DATABASE_URL：
```
DATABASE_URL=mysql+pymysql://appuser:secure_password_here@localhost:3306/ai_app_store
```

## 运行应用

### 1. 开发环境运行

```bash
# 激活虚拟环境
source venv/bin/activate

# 运行应用
python3.11 src/main.py
```

应用将在 http://localhost:5000 启动。

### 2. 后台运行

```bash
# 使用 nohup 后台运行
nohup python3.11 src/main.py > app.log 2>&1 &

# 查看进程
ps aux | grep python

# 停止应用
pkill -f "python3.11 src/main.py"
```

### 3. 验证应用运行

```bash
# 测试 API
curl http://localhost:5000/api/users

# 查看日志
tail -f app.log
```

## 生产环境部署

### 1. 安装 Gunicorn

```bash
source venv/bin/activate
pip install gunicorn
```

### 2. 创建 Gunicorn 配置

```bash
cat > gunicorn.conf.py << EOF
# Gunicorn 配置文件
import multiprocessing

# 服务器套接字
bind = "127.0.0.1:5000"
backlog = 2048

# 工作进程
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "sync"
worker_connections = 1000
timeout = 30
keepalive = 2

# 重启
max_requests = 1000
max_requests_jitter = 50
preload_app = True

# 日志
accesslog = "/opt/ai_app_store/ai_app_store/logs/access.log"
errorlog = "/opt/ai_app_store/ai_app_store/logs/error.log"
loglevel = "info"

# 进程命名
proc_name = "ai_app_store"

# 用户和组
user = "appuser"
group = "appuser"
EOF
```

### 3. 创建日志目录

```bash
mkdir -p logs
touch logs/access.log logs/error.log
```

### 4. 测试 Gunicorn

```bash
# 测试配置
gunicorn --check-config -c gunicorn.conf.py src.main:app

# 运行 Gunicorn
gunicorn -c gunicorn.conf.py src.main:app
```

## 系统服务配置

### 1. 创建 Systemd 服务文件

```bash
sudo tee /etc/systemd/system/ai-app-store.service > /dev/null << EOF
[Unit]
Description=AI App Store Flask Application
After=network.target

[Service]
Type=exec
User=appuser
Group=appuser
WorkingDirectory=/opt/ai_app_store/ai_app_store
Environment=PATH=/opt/ai_app_store/ai_app_store/venv/bin
ExecStart=/opt/ai_app_store/ai_app_store/venv/bin/gunicorn -c gunicorn.conf.py src.main:app
ExecReload=/bin/kill -s HUP \$MAINPID
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF
```

### 2. 启用和启动服务

```bash
# 重新加载 systemd
sudo systemctl daemon-reload

# 启用服务 (开机自启)
sudo systemctl enable ai-app-store

# 启动服务
sudo systemctl start ai-app-store

# 查看状态
sudo systemctl status ai-app-store

# 查看日志
sudo journalctl -u ai-app-store -f
```

### 3. 服务管理命令

```bash
# 停止服务
sudo systemctl stop ai-app-store

# 重启服务
sudo systemctl restart ai-app-store

# 重新加载配置
sudo systemctl reload ai-app-store

# 查看服务状态
sudo systemctl is-active ai-app-store
```

## Nginx 反向代理

### 1. 安装 Nginx

```bash
sudo apt install -y nginx
```

### 2. 配置 Nginx

创建站点配置：

```bash
sudo tee /etc/nginx/sites-available/ai-app-store << EOF
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;  # 替换为你的域名

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # 日志
    access_log /var/log/nginx/ai-app-store.access.log;
    error_log /var/log/nginx/ai-app-store.error.log;

    # 静态文件
    location /static/ {
        alias /opt/ai_app_store/ai_app_store/src/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API 和应用
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
    }

    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
EOF
```

### 3. 启用站点

```bash
# 创建符号链接
sudo ln -s /etc/nginx/sites-available/ai-app-store /etc/nginx/sites-enabled/

# 删除默认站点 (可选)
sudo rm -f /etc/nginx/sites-enabled/default

# 测试配置
sudo nginx -t

# 重启 Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

### 4. 配置 Nginx 优化

编辑 `/etc/nginx/nginx.conf`：

```bash
sudo tee -a /etc/nginx/nginx.conf << EOF
# 在 http 块中添加以下配置

# Gzip 压缩
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_proxied any;
gzip_comp_level 6;
gzip_types
    text/plain
    text/css
    text/xml
    text/javascript
    application/json
    application/javascript
    application/xml+rss
    application/atom+xml
    image/svg+xml;

# 文件上传大小限制
client_max_body_size 10M;

# 连接优化
keepalive_timeout 65;
keepalive_requests 100;

# 工作进程优化
worker_processes auto;
worker_connections 1024;
EOF
```

## SSL/HTTPS 配置

### 1. 安装 Certbot

```bash
sudo apt install -y certbot python3-certbot-nginx
```

### 2. 获取 SSL 证书

```bash
# 替换为你的域名和邮箱
sudo certbot --nginx -d your-domain.com -d www.your-domain.com --email <EMAIL> --agree-tos --no-eff-email
```

### 3. 自动续期

```bash
# 测试自动续期
sudo certbot renew --dry-run

# 查看定时任务
sudo systemctl status certbot.timer
```

### 4. 强制 HTTPS 重定向

Certbot 会自动配置 HTTPS 重定向，但你也可以手动添加：

```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

## 监控和日志

### 1. 日志管理

#### 1.1 配置日志轮转

```bash
sudo tee /etc/logrotate.d/ai-app-store << EOF
/opt/ai_app_store/ai_app_store/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 appuser appuser
    postrotate
        systemctl reload ai-app-store
    endscript
}
EOF
```

#### 1.2 查看日志

```bash
# 应用日志
tail -f /opt/ai_app_store/ai_app_store/logs/error.log
tail -f /opt/ai_app_store/ai_app_store/logs/access.log

# 系统服务日志
sudo journalctl -u ai-app-store -f

# Nginx 日志
sudo tail -f /var/log/nginx/ai-app-store.access.log
sudo tail -f /var/log/nginx/ai-app-store.error.log
```

### 2. 系统监控

#### 2.1 安装监控工具

```bash
sudo apt install -y htop iotop nethogs
```

#### 2.2 创建监控脚本

```bash
cat > monitor.sh << 'EOF'
#!/bin/bash

# 系统监控脚本
echo "=== 系统状态监控 $(date) ==="

# CPU 和内存使用
echo "CPU 和内存使用:"
top -bn1 | grep "Cpu(s)" | awk '{print "CPU: " $2 " " $3 " " $4 " " $5}'
free -h | grep "Mem:" | awk '{print "内存: " $3 "/" $2 " (" $3/$2*100 "%)"}'

# 磁盘使用
echo -e "\n磁盘使用:"
df -h | grep -E "/$|/opt"

# 网络连接
echo -e "\n网络连接:"
ss -tuln | grep :5000
ss -tuln | grep :80

# 应用进程
echo -e "\n应用进程:"
ps aux | grep -E "(gunicorn|nginx)" | grep -v grep

# 服务状态
echo -e "\n服务状态:"
systemctl is-active ai-app-store nginx postgresql

echo "================================"
EOF

chmod +x monitor.sh
```

#### 2.3 设置定时监控

```bash
# 添加到 crontab
(crontab -l 2>/dev/null; echo "*/5 * * * * /opt/ai_app_store/ai_app_store/monitor.sh >> /opt/ai_app_store/ai_app_store/logs/monitor.log 2>&1") | crontab -
```

### 3. 性能监控

#### 3.1 安装 htop

```bash
sudo apt install -y htop
```

#### 3.2 使用 htop 监控

```bash
htop
```

#### 3.3 网络监控

```bash
# 实时网络使用
sudo nethogs

# 网络连接统计
ss -s
```

## 常见问题

### 1. 权限问题

#### Q: 应用无法写入日志文件
**A**: 检查文件权限：
```bash
sudo chown -R appuser:appuser /opt/ai_app_store/ai_app_store/logs/
sudo chmod -R 755 /opt/ai_app_store/ai_app_store/logs/
```

#### Q: 数据库权限错误
**A**: 确保数据库文件权限正确：
```bash
sudo chown -R appuser:appuser /opt/ai_app_store/ai_app_store/src/database/
sudo chmod 644 /opt/ai_app_store/ai_app_store/src/database/app.db
```

### 2. 网络问题

#### Q: 无法访问应用
**A**: 检查防火墙和端口：
```bash
sudo ufw status
sudo netstat -tlnp | grep :5000
curl -I http://localhost:5000
```

#### Q: Nginx 502 错误
**A**: 检查后端服务：
```bash
sudo systemctl status ai-app-store
curl http://127.0.0.1:5000
sudo nginx -t
```

### 3. 数据库问题

#### Q: PostgreSQL 连接失败
**A**: 检查 PostgreSQL 服务和配置：
```bash
sudo systemctl status postgresql
sudo -u postgres psql -c "\l"
```

#### Q: 数据库迁移失败
**A**: 手动初始化数据库：
```bash
source venv/bin/activate
python3.11 -c "from src.main import app, db; app.app_context().push(); db.create_all()"
```

### 4. 性能问题

#### Q: 应用响应慢
**A**: 优化配置：
- 增加 Gunicorn worker 数量
- 配置数据库连接池
- 启用 Nginx 缓存
- 使用 Redis 缓存

#### Q: 内存使用过高
**A**: 
```bash
# 检查内存使用
free -h
ps aux --sort=-%mem | head

# 重启服务释放内存
sudo systemctl restart ai-app-store
```

## 性能优化

### 1. Gunicorn 优化

```python
# gunicorn.conf.py 优化配置
import multiprocessing

# 根据 CPU 核心数调整 worker 数量
workers = multiprocessing.cpu_count() * 2 + 1

# 使用异步 worker (可选)
worker_class = "gevent"
worker_connections = 1000

# 内存管理
max_requests = 1000
max_requests_jitter = 100

# 预加载应用
preload_app = True
```

### 2. 数据库优化

#### 2.1 PostgreSQL 优化

编辑 `/etc/postgresql/14/main/postgresql.conf`：

```bash
# 内存设置
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB

# 连接设置
max_connections = 100

# 日志设置
log_min_duration_statement = 1000
```

#### 2.2 添加数据库索引

```python
# 在模型中添加索引
class App(db.Model):
    # 为常用查询字段添加索引
    name = db.Column(db.String(200), nullable=False, index=True)
    status = db.Column(db.String(20), default='active', index=True)
    featured = db.Column(db.Boolean, default=False, index=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
```

### 3. Nginx 缓存优化

```nginx
# 在 server 块中添加缓存配置
location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# API 缓存 (谨慎使用)
location /api/apps {
    proxy_cache_valid 200 5m;
    proxy_cache_key $request_uri;
}
```

### 4. 系统级优化

#### 4.1 调整文件描述符限制

```bash
# 编辑 limits.conf
sudo tee -a /etc/security/limits.conf << EOF
appuser soft nofile 65536
appuser hard nofile 65536
EOF
```

#### 4.2 内核参数优化

```bash
sudo tee -a /etc/sysctl.conf << EOF
# 网络优化
net.core.somaxconn = 65535
net.ipv4.tcp_max_syn_backlog = 65535
net.core.netdev_max_backlog = 5000

# 内存优化
vm.swappiness = 10
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5
EOF

# 应用配置
sudo sysctl -p
```

## 备份和恢复

### 1. 自动备份脚本

```bash
cat > backup.sh << 'EOF'
#!/bin/bash

BACKUP_DIR="/opt/backups/ai_app_store"
DATE=$(date +%Y%m%d_%H%M%S)
APP_DIR="/opt/ai_app_store/ai_app_store"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份应用代码
tar -czf $BACKUP_DIR/app_$DATE.tar.gz -C /opt/ai_app_store ai_app_store

# 备份数据库
if [ -f "$APP_DIR/src/database/app.db" ]; then
    cp $APP_DIR/src/database/app.db $BACKUP_DIR/database_$DATE.db
fi

# 备份配置文件
cp $APP_DIR/.env $BACKUP_DIR/env_$DATE.backup

# 清理旧备份 (保留30天)
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
find $BACKUP_DIR -name "*.db" -mtime +30 -delete
find $BACKUP_DIR -name "*.backup" -mtime +30 -delete

echo "备份完成: $DATE"
EOF

chmod +x backup.sh
```

### 2. 设置定时备份

```bash
# 添加到 crontab (每天凌晨2点备份)
(crontab -l 2>/dev/null; echo "0 2 * * * /opt/ai_app_store/ai_app_store/backup.sh >> /opt/ai_app_store/ai_app_store/logs/backup.log 2>&1") | crontab -
```

### 3. 恢复流程

```bash
# 停止服务
sudo systemctl stop ai-app-store nginx

# 恢复应用代码
cd /opt/ai_app_store
tar -xzf /opt/backups/ai_app_store/app_YYYYMMDD_HHMMSS.tar.gz

# 恢复数据库
cp /opt/backups/ai_app_store/database_YYYYMMDD_HHMMSS.db ai_app_store/src/database/app.db

# 恢复配置
cp /opt/backups/ai_app_store/env_YYYYMMDD_HHMMSS.backup ai_app_store/.env

# 重启服务
sudo systemctl start ai-app-store nginx
```

## 安全加固

### 1. 系统安全

```bash
# 禁用 root SSH 登录
sudo sed -i 's/#PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
sudo systemctl restart ssh

# 配置 fail2ban
sudo apt install -y fail2ban
sudo systemctl enable fail2ban
sudo systemctl start fail2ban
```

### 2. 应用安全

```python
# 在 main.py 中添加安全头
from flask_talisman import Talisman

# 启用安全头
Talisman(app, force_https=True)

# 限制请求大小
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB
```

### 3. 数据库安全

```bash
# PostgreSQL 安全配置
sudo -u postgres psql -c "ALTER USER appuser SET default_transaction_isolation TO 'read committed';"
sudo -u postgres psql -c "ALTER USER appuser SET timezone TO 'UTC';"
```

## 总结

本文档提供了在 Ubuntu 22.04 系统上部署 AI App Store 后台系统的完整指南。通过遵循这些步骤，你可以建立一个安全、高性能的生产环境。

关键要点：
- 使用专门的应用用户提高安全性
- 配置 Systemd 服务实现自动启动和管理
- 使用 Nginx 作为反向代理提高性能
- 配置 SSL/HTTPS 确保数据传输安全
- 实施监控和日志管理
- 定期备份数据和配置

建议在生产环境中：
- 使用强密码和密钥认证
- 定期更新系统和依赖包
- 监控系统性能和安全日志
- 实施灾难恢复计划

如果遇到问题，请参考常见问题部分或查看相关日志文件。

