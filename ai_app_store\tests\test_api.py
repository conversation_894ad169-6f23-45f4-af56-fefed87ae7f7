import unittest
import json
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from src.main import app
from src.models.user import db, User
from src.models.app import App, Category, Review, AppLike

class APITestCase(unittest.TestCase):
    def setUp(self):
        """测试前的设置"""
        app.config['TESTING'] = True
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        self.app = app.test_client()
        
        with app.app_context():
            db.create_all()
            self.create_test_data()
    
    def tearDown(self):
        """测试后的清理"""
        with app.app_context():
            db.session.remove()
            db.drop_all()
    
    def create_test_data(self):
        """创建测试数据"""
        # 创建测试用户
        user1 = User(
            username='testuser1',
            email='<EMAIL>',
            full_name='Test User 1',
            bio='Test bio'
        )
        user2 = User(
            username='testuser2',
            email='<EMAIL>',
            full_name='Test User 2'
        )
        
        # 创建测试分类
        category1 = Category(
            name='AI Tools',
            description='AI-powered applications'
        )
        category2 = Category(
            name='Web Apps',
            description='Web applications'
        )
        
        db.session.add_all([user1, user2, category1, category2])
        db.session.commit()
        
        # 创建测试应用
        app1 = App(
            name='Test App 1',
            description='A test application',
            short_description='Test app',
            vercel_url='https://test-app-1.vercel.app',
            github_url='https://github.com/test/app1',
            tags=['ai', 'test'],
            tech_stack=['React', 'Python'],
            ai_framework='OpenAI',
            user_id=user1.id,
            category_id=category1.id
        )
        
        app2 = App(
            name='Test App 2',
            description='Another test application',
            short_description='Another test app',
            vercel_url='https://test-app-2.vercel.app',
            user_id=user2.id,
            category_id=category2.id,
            featured=True
        )
        
        db.session.add_all([app1, app2])
        db.session.commit()
        
        # 保存测试数据 ID
        self.user1_id = user1.id
        self.user2_id = user2.id
        self.category1_id = category1.id
        self.category2_id = category2.id
        self.app1_id = app1.id
        self.app2_id = app2.id

class UserAPITest(APITestCase):
    """用户 API 测试"""
    
    def test_get_users(self):
        """测试获取用户列表"""
        response = self.app.get('/api/users')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertIn('users', data['data'])
        self.assertEqual(len(data['data']['users']), 2)
    
    def test_get_user_by_id(self):
        """测试获取单个用户"""
        response = self.app.get(f'/api/users/{self.user1_id}')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertEqual(data['data']['username'], 'testuser1')
    
    def test_create_user(self):
        """测试创建用户"""
        user_data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'full_name': 'New User'
        }
        
        response = self.app.post('/api/users',
                               data=json.dumps(user_data),
                               content_type='application/json')
        self.assertEqual(response.status_code, 201)
        
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertEqual(data['data']['username'], 'newuser')
    
    def test_create_user_duplicate_username(self):
        """测试创建重复用户名的用户"""
        user_data = {
            'username': 'testuser1',  # 已存在的用户名
            'email': '<EMAIL>'
        }
        
        response = self.app.post('/api/users',
                               data=json.dumps(user_data),
                               content_type='application/json')
        self.assertEqual(response.status_code, 400)
        
        data = json.loads(response.data)
        self.assertFalse(data['success'])
    
    def test_update_user(self):
        """测试更新用户"""
        update_data = {
            'full_name': 'Updated Name',
            'bio': 'Updated bio'
        }
        
        response = self.app.put(f'/api/users/{self.user1_id}',
                              data=json.dumps(update_data),
                              content_type='application/json')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertEqual(data['data']['full_name'], 'Updated Name')

class CategoryAPITest(APITestCase):
    """分类 API 测试"""
    
    def test_get_categories(self):
        """测试获取分类列表"""
        response = self.app.get('/api/categories')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertEqual(len(data['data']), 2)
    
    def test_create_category(self):
        """测试创建分类"""
        category_data = {
            'name': 'New Category',
            'description': 'A new category'
        }
        
        response = self.app.post('/api/categories',
                               data=json.dumps(category_data),
                               content_type='application/json')
        self.assertEqual(response.status_code, 201)
        
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertEqual(data['data']['name'], 'New Category')

class AppAPITest(APITestCase):
    """应用 API 测试"""
    
    def test_get_apps(self):
        """测试获取应用列表"""
        response = self.app.get('/api/apps')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertIn('apps', data['data'])
        self.assertEqual(len(data['data']['apps']), 2)
    
    def test_get_app_by_id(self):
        """测试获取单个应用"""
        response = self.app.get(f'/api/apps/{self.app1_id}')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertEqual(data['data']['name'], 'Test App 1')
    
    def test_create_app(self):
        """测试创建应用"""
        app_data = {
            'name': 'New App',
            'description': 'A new application',
            'vercel_url': 'https://new-app.vercel.app',
            'user_id': self.user1_id,
            'category_id': self.category1_id,
            'tags': ['new', 'test'],
            'tech_stack': ['React']
        }
        
        response = self.app.post('/api/apps',
                               data=json.dumps(app_data),
                               content_type='application/json')
        self.assertEqual(response.status_code, 201)
        
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertEqual(data['data']['name'], 'New App')
    
    def test_get_featured_apps(self):
        """测试获取推荐应用"""
        response = self.app.get('/api/apps?featured=true')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertEqual(len(data['data']['apps']), 1)
        self.assertTrue(data['data']['apps'][0]['featured'])
    
    def test_search_apps(self):
        """测试搜索应用"""
        response = self.app.get('/api/apps?search=Test App 1')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertEqual(len(data['data']['apps']), 1)
        self.assertEqual(data['data']['apps'][0]['name'], 'Test App 1')
    
    def test_create_review(self):
        """测试创建评论"""
        review_data = {
            'rating': 5,
            'comment': 'Great app!',
            'user_id': self.user2_id
        }
        
        response = self.app.post(f'/api/apps/{self.app1_id}/reviews',
                               data=json.dumps(review_data),
                               content_type='application/json')
        self.assertEqual(response.status_code, 201)
        
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertEqual(data['data']['rating'], 5)
    
    def test_toggle_app_like(self):
        """测试应用点赞"""
        like_data = {
            'user_id': self.user2_id
        }
        
        # 点赞
        response = self.app.post(f'/api/apps/{self.app1_id}/like',
                               data=json.dumps(like_data),
                               content_type='application/json')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertTrue(data['data']['liked'])
        
        # 取消点赞
        response = self.app.post(f'/api/apps/{self.app1_id}/like',
                               data=json.dumps(like_data),
                               content_type='application/json')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertFalse(data['data']['liked'])

if __name__ == '__main__':
    unittest.main()

