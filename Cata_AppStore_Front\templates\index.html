<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>App Store - 本地应用商店</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-store"></i> App Store
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showApps()">应用</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showCategories()">分类</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <div id="auth-buttons">
                        <button class="btn btn-outline-light me-2" onclick="showLogin()">登录</button>
                        <button class="btn btn-light" onclick="showRegister()">注册</button>
                    </div>
                    <div id="user-info" class="d-none">
                        <span class="navbar-text me-2">
                            欢迎，<span id="username"></span>
                        </span>
                        <button class="btn btn-outline-light me-2" onclick="showMyApps()">我的应用</button>
                        <button class="btn btn-outline-light me-2" onclick="showUpload()">上传应用</button>
                        <button class="btn btn-outline-light me-2" id="admin-btn" onclick="showAdmin()" style="display: none;">管理后台</button>
                        <button class="btn btn-outline-light" onclick="logout()">退出</button>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
        <!-- 首页 -->
        <div id="home-page" class="page">
            <div class="jumbotron bg-light p-5 rounded mb-4">
                <h1 class="display-4">欢迎使用 App Store</h1>
                <p class="lead">本地应用商店，支持应用上传、分享和管理</p>
                <hr class="my-4">
                <p>上传您的应用，与社区分享，发现更多有趣的应用</p>
                <button class="btn btn-primary btn-lg" onclick="showApps()" role="button">浏览应用</button>
            </div>

            <!-- 推荐应用 -->
            <div class="row">
                <div class="col-12">
                    <h3>推荐应用</h3>
                    <div id="featured-apps" class="row">
                        <!-- 动态加载 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 登录页面 -->
        <div id="login-page" class="page d-none">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3>用户登录</h3>
                        </div>
                        <div class="card-body">
                            <form id="login-form">
                                <div class="mb-3">
                                    <label for="login-username" class="form-label">用户名</label>
                                    <input type="text" class="form-control" id="login-username" required>
                                </div>
                                <div class="mb-3">
                                    <label for="login-password" class="form-label">密码</label>
                                    <input type="password" class="form-control" id="login-password" required>
                                </div>
                                <button type="submit" class="btn btn-primary">登录</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 注册页面 -->
        <div id="register-page" class="page d-none">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3>用户注册</h3>
                        </div>
                        <div class="card-body">
                            <form id="register-form">
                                <div class="mb-3">
                                    <label for="register-username" class="form-label">用户名</label>
                                    <input type="text" class="form-control" id="register-username" required>
                                </div>
                                <div class="mb-3">
                                    <label for="register-email" class="form-label">邮箱</label>
                                    <input type="email" class="form-control" id="register-email" required>
                                </div>
                                <div class="mb-3">
                                    <label for="register-fullname" class="form-label">姓名</label>
                                    <input type="text" class="form-control" id="register-fullname">
                                </div>
                                <div class="mb-3">
                                    <label for="register-password" class="form-label">密码</label>
                                    <input type="password" class="form-control" id="register-password" required>
                                </div>
                                <button type="submit" class="btn btn-primary">注册</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 应用列表页面 -->
        <div id="apps-page" class="page d-none">
            <div class="row">
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-header">
                            <h5>筛选</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="category-filter" class="form-label">分类</label>
                                <select class="form-select" id="category-filter" onchange="filterApps()">
                                    <option value="">全部分类</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-9">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h3>应用列表</h3>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary" onclick="sortApps('newest')">最新</button>
                            <button type="button" class="btn btn-outline-primary" onclick="sortApps('popular')">最受欢迎</button>
                        </div>
                    </div>
                    <div id="apps-list" class="row">
                        <!-- 动态加载 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 上传应用页面 -->
        <div id="upload-page" class="page d-none">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h3>上传应用</h3>
                        </div>
                        <div class="card-body">
                            <form id="upload-form" enctype="multipart/form-data">
                                <div class="mb-3">
                                    <label for="app-title" class="form-label">应用标题 *</label>
                                    <input type="text" class="form-control" id="app-title" required>
                                </div>
                                <div class="mb-3">
                                    <label for="app-url" class="form-label">应用URL *</label>
                                    <input type="url" class="form-control" id="app-url" placeholder="https://example.com" required>
                                </div>
                                <div class="mb-3">
                                    <label for="app-description" class="form-label">应用描述</label>
                                    <textarea class="form-control" id="app-description" rows="3"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="app-category" class="form-label">分类</label>
                                    <select class="form-select" id="app-category">
                                        <option value="">选择分类</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="app-tags" class="form-label">标签</label>
                                    <input type="text" class="form-control" id="app-tags" placeholder="标签1, 标签2, 标签3">
                                </div>
                                <div class="mb-3">
                                    <label for="app-zip" class="form-label">ZIP文件</label>
                                    <input type="file" class="form-control" id="app-zip" accept=".zip">
                                    <div class="form-text">上传ZIP文件将自动解压供用户下载</div>
                                </div>
                                <button type="submit" class="btn btn-primary">上传应用</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 我的应用页面 -->
        <div id="my-apps-page" class="page d-none">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h3>我的应用</h3>
                <button class="btn btn-primary" onclick="showUpload()">上传新应用</button>
            </div>
            <div id="my-apps-list" class="row">
                <!-- 动态加载 -->
            </div>
        </div>

        <!-- 管理后台页面 -->
        <div id="admin-page" class="page d-none">
            <div class="row">
                <div class="col-md-12">
                    <h3>管理后台</h3>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h5 id="total-users">0</h5>
                                    <p class="text-muted">总用户数</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h5 id="total-apps">0</h5>
                                    <p class="text-muted">总应用数</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h5 id="pending-apps">0</h5>
                                    <p class="text-muted">待审核</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h5 id="total-categories">0</h5>
                                    <p class="text-muted">分类数</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <h4>待审核应用</h4>
                        <div id="pending-apps-list" class="row">
                            <!-- 动态加载 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 通知 -->
    <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 5">
        <div id="toast" class="toast" role="alert">
            <div class="toast-header">
                <strong class="me-auto">通知</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="toast-message">
                <!-- 动态消息 -->
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/api.js"></script>
    <script src="/static/js/app.js"></script>
</body>
</html>