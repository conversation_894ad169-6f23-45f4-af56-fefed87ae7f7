# 管理员界面和功能系统

## 1. 系统概述

为 Catai App Store 创建完整的管理员后台系统，提供用户管理、应用审核、系统监控和数据分析功能。

### 核心功能模块
1. **用户管理**：查看、编辑、禁用用户账户
2. **应用审核**：审核待发布应用，管理应用状态
3. **内容管理**：管理分类、标签、推荐内容
4. **系统监控**：查看系统统计、性能指标
5. **设置管理**：系统配置、权限设置

## 2. 管理员界面架构

### 2.1 页面结构
```
/admin
├── /dashboard          // 管理员仪表板
├── /users             // 用户管理
│   ├── /list          // 用户列表
│   ├── /detail/:id    // 用户详情
│   └── /roles         // 角色管理
├── /apps              // 应用管理
│   ├── /pending       // 待审核应用
│   ├── /approved      // 已批准应用
│   ├── /rejected      // 已拒绝应用
│   └── /detail/:id    // 应用详情
├── /content           // 内容管理
│   ├── /categories    // 分类管理
│   ├── /featured      // 推荐内容
│   └── /announcements // 公告管理
├── /analytics         // 数据分析
│   ├── /overview      // 概览统计
│   ├── /users         // 用户分析
│   └── /apps          // 应用分析
└── /settings          // 系统设置
    ├── /general       // 基本设置
    ├── /permissions   // 权限设置
    └── /maintenance   // 维护模式
```

### 2.2 组件架构
```
AdminLayout
├── AdminSidebar       // 侧边导航
├── AdminHeader        // 顶部导航
├── AdminBreadcrumb    // 面包屑导航
└── AdminContent       // 主要内容区域
    ├── Dashboard      // 仪表板组件
    ├── UserManagement // 用户管理组件
    ├── AppReview      // 应用审核组件
    ├── ContentManager // 内容管理组件
    ├── Analytics      // 分析组件
    └── Settings       // 设置组件
```

## 3. 前端实现

### 3.1 管理员布局组件
```jsx
// components/admin/AdminLayout.jsx
import React from 'react';
import { AdminSidebar } from './AdminSidebar';
import { AdminHeader } from './AdminHeader';
import { AdminBreadcrumb } from './AdminBreadcrumb';
import { useCurrentUser } from '../../hooks/useCurrentUser';
import { PermissionGuard } from '../PermissionGuard';

export function AdminLayout({ children }) {
  const { user } = useCurrentUser();

  return (
    <PermissionGuard requiredRole="admin" fallback={<AccessDenied />}>
      <div className="admin-layout">
        <AdminSidebar />
        <div className="admin-main">
          <AdminHeader user={user} />
          <AdminBreadcrumb />
          <main className="admin-content">
            {children}
          </main>
        </div>
      </div>
    </PermissionGuard>
  );
}

function AccessDenied() {
  return (
    <div className="access-denied">
      <h1>访问被拒绝</h1>
      <p>您没有权限访问管理员界面</p>
    </div>
  );
}
```

### 3.2 管理员仪表板
```jsx
// components/admin/Dashboard.jsx
import React, { useState, useEffect } from 'react';
import { StatCard } from './StatCard';
import { RecentActivity } from './RecentActivity';
import { QuickActions } from './QuickActions';
import { getAdminStats } from '../../utils/adminApi';

export function Dashboard() {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function loadStats() {
      try {
        const data = await getAdminStats();
        setStats(data);
      } catch (error) {
        console.error('Failed to load admin stats:', error);
      } finally {
        setLoading(false);
      }
    }
    
    loadStats();
  }, []);

  if (loading) {
    return <div className="loading">加载中...</div>;
  }

  return (
    <div className="admin-dashboard">
      <div className="dashboard-header">
        <h1>管理员仪表板</h1>
        <p>欢迎回来！这里是您的应用商店管理中心。</p>
      </div>

      <div className="stats-grid">
        <StatCard
          title="总用户数"
          value={stats?.totalUsers || 0}
          change={stats?.userGrowth || 0}
          icon="👥"
          color="blue"
        />
        <StatCard
          title="总应用数"
          value={stats?.totalApps || 0}
          change={stats?.appGrowth || 0}
          icon="📱"
          color="green"
        />
        <StatCard
          title="待审核应用"
          value={stats?.pendingApps || 0}
          change={stats?.pendingChange || 0}
          icon="⏳"
          color="orange"
        />
        <StatCard
          title="今日下载量"
          value={stats?.todayDownloads || 0}
          change={stats?.downloadGrowth || 0}
          icon="⬇️"
          color="purple"
        />
      </div>

      <div className="dashboard-content">
        <div className="dashboard-left">
          <RecentActivity activities={stats?.recentActivities || []} />
        </div>
        <div className="dashboard-right">
          <QuickActions />
        </div>
      </div>
    </div>
  );
}
```

### 3.3 用户管理组件
```jsx
// components/admin/UserManagement.jsx
import React, { useState, useEffect } from 'react';
import { DataTable } from './DataTable';
import { UserModal } from './UserModal';
import { SearchFilter } from './SearchFilter';
import { getUsers, updateUser, deleteUser } from '../../utils/adminApi';

export function UserManagement() {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedUser, setSelectedUser] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [filters, setFilters] = useState({
    search: '',
    role: '',
    status: ''
  });

  useEffect(() => {
    loadUsers();
  }, [filters]);

  async function loadUsers() {
    try {
      setLoading(true);
      const data = await getUsers(filters);
      setUsers(data.items);
    } catch (error) {
      console.error('Failed to load users:', error);
    } finally {
      setLoading(false);
    }
  }

  const handleEditUser = (user) => {
    setSelectedUser(user);
    setShowModal(true);
  };

  const handleDeleteUser = async (userId) => {
    if (window.confirm('确定要删除这个用户吗？')) {
      try {
        await deleteUser(userId);
        await loadUsers();
      } catch (error) {
        console.error('Failed to delete user:', error);
        alert('删除用户失败');
      }
    }
  };

  const handleSaveUser = async (userData) => {
    try {
      if (selectedUser) {
        await updateUser(selectedUser._id, userData);
      }
      setShowModal(false);
      setSelectedUser(null);
      await loadUsers();
    } catch (error) {
      console.error('Failed to save user:', error);
      alert('保存用户失败');
    }
  };

  const columns = [
    {
      key: 'email',
      title: '邮箱',
      render: (user) => (
        <div className="user-info">
          <div className="user-email">{user.loginEmail}</div>
          <div className="user-name">{user.profile?.nickname || '未设置'}</div>
        </div>
      )
    },
    {
      key: 'role',
      title: '角色',
      render: (user) => (
        <span className={`role-badge role-${user.customFields?.role || 'member'}`}>
          {user.customFields?.role === 'admin' ? '管理员' : '普通用户'}
        </span>
      )
    },
    {
      key: 'uploadedApps',
      title: '上传应用数',
      render: (user) => user.customFields?.uploadedAppsCount || 0
    },
    {
      key: 'status',
      title: '状态',
      render: (user) => (
        <span className={`status-badge ${user.customFields?.isActive ? 'active' : 'inactive'}`}>
          {user.customFields?.isActive ? '活跃' : '禁用'}
        </span>
      )
    },
    {
      key: 'joinDate',
      title: '注册时间',
      render: (user) => new Date(user._createdDate).toLocaleDateString()
    },
    {
      key: 'actions',
      title: '操作',
      render: (user) => (
        <div className="action-buttons">
          <button 
            onClick={() => handleEditUser(user)}
            className="btn btn-sm btn-primary"
          >
            编辑
          </button>
          <button 
            onClick={() => handleDeleteUser(user._id)}
            className="btn btn-sm btn-danger"
          >
            删除
          </button>
        </div>
      )
    }
  ];

  return (
    <div className="user-management">
      <div className="page-header">
        <h1>用户管理</h1>
        <button 
          onClick={() => setShowModal(true)}
          className="btn btn-primary"
        >
          添加用户
        </button>
      </div>

      <SearchFilter
        filters={filters}
        onFiltersChange={setFilters}
        options={{
          roles: [
            { value: '', label: '全部角色' },
            { value: 'member', label: '普通用户' },
            { value: 'admin', label: '管理员' }
          ],
          statuses: [
            { value: '', label: '全部状态' },
            { value: 'active', label: '活跃' },
            { value: 'inactive', label: '禁用' }
          ]
        }}
      />

      <DataTable
        data={users}
        columns={columns}
        loading={loading}
        emptyMessage="暂无用户数据"
      />

      {showModal && (
        <UserModal
          user={selectedUser}
          onSave={handleSaveUser}
          onClose={() => {
            setShowModal(false);
            setSelectedUser(null);
          }}
        />
      )}
    </div>
  );
}
```

### 3.4 应用审核组件
```jsx
// components/admin/AppReview.jsx
import React, { useState, useEffect } from 'react';
import { AppCard } from './AppCard';
import { AppDetailModal } from './AppDetailModal';
import { FilterTabs } from './FilterTabs';
import { getPendingApps, approveApp, rejectApp } from '../../utils/adminApi';

export function AppReview() {
  const [apps, setApps] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedApp, setSelectedApp] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [activeTab, setActiveTab] = useState('pending');

  useEffect(() => {
    loadApps();
  }, [activeTab]);

  async function loadApps() {
    try {
      setLoading(true);
      const data = await getPendingApps(activeTab);
      setApps(data.items);
    } catch (error) {
      console.error('Failed to load apps:', error);
    } finally {
      setLoading(false);
    }
  }

  const handleViewApp = (app) => {
    setSelectedApp(app);
    setShowModal(true);
  };

  const handleApproveApp = async (appId, reason = '') => {
    try {
      await approveApp(appId, reason);
      await loadApps();
    } catch (error) {
      console.error('Failed to approve app:', error);
      alert('批准应用失败');
    }
  };

  const handleRejectApp = async (appId, reason) => {
    if (!reason) {
      reason = prompt('请输入拒绝原因：');
      if (!reason) return;
    }
    
    try {
      await rejectApp(appId, reason);
      await loadApps();
    } catch (error) {
      console.error('Failed to reject app:', error);
      alert('拒绝应用失败');
    }
  };

  const tabs = [
    { key: 'pending', label: '待审核', count: apps.filter(app => app.status === 'pending').length },
    { key: 'approved', label: '已批准', count: apps.filter(app => app.status === 'approved').length },
    { key: 'rejected', label: '已拒绝', count: apps.filter(app => app.status === 'rejected').length }
  ];

  return (
    <div className="app-review">
      <div className="page-header">
        <h1>应用审核</h1>
        <p>审核用户提交的应用，确保应用质量和安全性</p>
      </div>

      <FilterTabs
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={setActiveTab}
      />

      {loading ? (
        <div className="loading">加载中...</div>
      ) : (
        <div className="apps-grid">
          {apps.length === 0 ? (
            <div className="empty-state">
              <p>暂无{tabs.find(t => t.key === activeTab)?.label}的应用</p>
            </div>
          ) : (
            apps.map(app => (
              <AppCard
                key={app._id}
                app={app}
                onView={() => handleViewApp(app)}
                onApprove={() => handleApproveApp(app._id)}
                onReject={() => handleRejectApp(app._id)}
                showActions={activeTab === 'pending'}
              />
            ))
          )}
        </div>
      )}

      {showModal && selectedApp && (
        <AppDetailModal
          app={selectedApp}
          onApprove={(reason) => {
            handleApproveApp(selectedApp._id, reason);
            setShowModal(false);
          }}
          onReject={(reason) => {
            handleRejectApp(selectedApp._id, reason);
            setShowModal(false);
          }}
          onClose={() => {
            setShowModal(false);
            setSelectedApp(null);
          }}
        />
      )}
    </div>
  );
}
```

## 4. 后端 API 实现

### 4.1 管理员 API 工具函数
```javascript
// utils/adminApi.js
import { items } from '@wix/data';
import { members } from '@wix/members';
import { authentication } from '@wix/members';

// 验证管理员权限
async function requireAdminAuth() {
  const currentUser = await getCurrentUser();
  if (!currentUser || currentUser.customFields?.role !== 'admin') {
    throw new Error('需要管理员权限');
  }
  return currentUser;
}

// 获取管理员统计数据
export async function getAdminStats() {
  await requireAdminAuth();
  
  try {
    // 获取用户统计
    const usersResult = await members.queryMembers().find();
    const totalUsers = usersResult.items.length;
    
    // 获取应用统计
    const appsResult = await items.queryDataItems('Apps').find();
    const totalApps = appsResult.items.length;
    const pendingApps = appsResult.items.filter(app => app.status === 'pending').length;
    
    // 计算今日下载量
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayDownloads = appsResult.items.reduce((sum, app) => {
      return sum + (app.downloadCount || 0);
    }, 0);
    
    // 获取最近活动
    const recentActivities = await getRecentActivities();
    
    return {
      totalUsers,
      totalApps,
      pendingApps,
      todayDownloads,
      userGrowth: calculateGrowth('users'),
      appGrowth: calculateGrowth('apps'),
      downloadGrowth: calculateGrowth('downloads'),
      recentActivities
    };
  } catch (error) {
    console.error('Failed to get admin stats:', error);
    throw error;
  }
}

// 获取用户列表
export async function getUsers(filters = {}) {
  await requireAdminAuth();
  
  try {
    let query = members.queryMembers();
    
    // 应用搜索过滤
    if (filters.search) {
      query = query.contains('loginEmail', filters.search);
    }
    
    // 应用角色过滤
    if (filters.role) {
      query = query.eq('customFields.role', filters.role);
    }
    
    // 应用状态过滤
    if (filters.status) {
      const isActive = filters.status === 'active';
      query = query.eq('customFields.isActive', isActive);
    }
    
    const result = await query.find();
    return result;
  } catch (error) {
    console.error('Failed to get users:', error);
    throw error;
  }
}

// 更新用户信息
export async function updateUser(userId, userData) {
  await requireAdminAuth();
  
  try {
    const updateData = {
      customFields: {
        role: userData.role,
        isActive: userData.isActive,
        uploadQuota: userData.uploadQuota
      }
    };
    
    if (userData.profile) {
      updateData.profile = userData.profile;
    }
    
    const result = await members.updateMember(userId, updateData);
    return result;
  } catch (error) {
    console.error('Failed to update user:', error);
    throw error;
  }
}

// 删除用户
export async function deleteUser(userId) {
  await requireAdminAuth();
  
  try {
    // 首先删除用户的所有应用
    const userApps = await items.queryDataItems('Apps')
      .eq('uploaderId', userId)
      .find();
    
    for (const app of userApps.items) {
      await items.removeDataItem('Apps', app._id);
    }
    
    // 然后删除用户
    const result = await members.deleteMember(userId);
    return result;
  } catch (error) {
    console.error('Failed to delete user:', error);
    throw error;
  }
}

// 获取待审核应用
export async function getPendingApps(status = 'pending') {
  await requireAdminAuth();
  
  try {
    const query = items.queryDataItems('Apps');
    
    if (status !== 'all') {
      query.eq('status', status);
    }
    
    const result = await query
      .descending('_createdDate')
      .find();
    
    return result;
  } catch (error) {
    console.error('Failed to get pending apps:', error);
    throw error;
  }
}

// 批准应用
export async function approveApp(appId, reason = '') {
  await requireAdminAuth();
  
  try {
    const result = await items.updateDataItem('Apps', appId, {
      status: 'approved',
      approvedDate: new Date(),
      approvalReason: reason
    });
    
    // 发送通知给应用上传者
    await sendAppStatusNotification(appId, 'approved', reason);
    
    return result;
  } catch (error) {
    console.error('Failed to approve app:', error);
    throw error;
  }
}

// 拒绝应用
export async function rejectApp(appId, reason) {
  await requireAdminAuth();
  
  try {
    const result = await items.updateDataItem('Apps', appId, {
      status: 'rejected',
      rejectedDate: new Date(),
      rejectionReason: reason
    });
    
    // 发送通知给应用上传者
    await sendAppStatusNotification(appId, 'rejected', reason);
    
    return result;
  } catch (error) {
    console.error('Failed to reject app:', error);
    throw error;
  }
}

// 发送应用状态通知
async function sendAppStatusNotification(appId, status, reason) {
  try {
    const app = await items.getDataItem('Apps', appId);
    const uploader = await members.getMember(app.uploaderId);
    
    // 这里可以集成邮件服务或站内消息系统
    console.log(`Notification sent to ${uploader.loginEmail}: App ${app.title} ${status}. Reason: ${reason}`);
  } catch (error) {
    console.error('Failed to send notification:', error);
  }
}

// 获取最近活动
async function getRecentActivities() {
  try {
    // 获取最近的应用提交
    const recentApps = await items.queryDataItems('Apps')
      .descending('_createdDate')
      .limit(5)
      .find();
    
    // 获取最近的用户注册
    const recentUsers = await members.queryMembers()
      .descending('_createdDate')
      .limit(5)
      .find();
    
    const activities = [];
    
    // 添加应用活动
    recentApps.items.forEach(app => {
      activities.push({
        type: 'app_submitted',
        message: `用户提交了应用 "${app.title}"`,
        timestamp: app._createdDate,
        user: app.uploaderEmail
      });
    });
    
    // 添加用户活动
    recentUsers.items.forEach(user => {
      activities.push({
        type: 'user_registered',
        message: `新用户注册: ${user.loginEmail}`,
        timestamp: user._createdDate,
        user: user.loginEmail
      });
    });
    
    // 按时间排序
    return activities
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      .slice(0, 10);
      
  } catch (error) {
    console.error('Failed to get recent activities:', error);
    return [];
  }
}

// 计算增长率（模拟实现）
function calculateGrowth(type) {
  // 这里应该实现真实的增长率计算
  // 比较当前周期和上一周期的数据
  return Math.floor(Math.random() * 20) - 10; // 模拟 -10% 到 +10% 的增长
}
```

### 4.2 分类管理 API
```javascript
// utils/categoryApi.js
import { items } from '@wix/data';

// 获取所有分类
export async function getCategories() {
  try {
    const result = await items.queryDataItems('Categories')
      .ascending('name')
      .find();
    return result;
  } catch (error) {
    console.error('Failed to get categories:', error);
    throw error;
  }
}

// 创建分类
export async function createCategory(categoryData) {
  await requireAdminAuth();
  
  try {
    const result = await items.insertDataItem('Categories', {
      ...categoryData,
      appCount: 0,
      isActive: true
    });
    return result;
  } catch (error) {
    console.error('Failed to create category:', error);
    throw error;
  }
}

// 更新分类
export async function updateCategory(categoryId, categoryData) {
  await requireAdminAuth();
  
  try {
    const result = await items.updateDataItem('Categories', categoryId, categoryData);
    return result;
  } catch (error) {
    console.error('Failed to update category:', error);
    throw error;
  }
}

// 删除分类
export async function deleteCategory(categoryId) {
  await requireAdminAuth();
  
  try {
    // 检查是否有应用使用此分类
    const appsInCategory = await items.queryDataItems('Apps')
      .eq('category', categoryId)
      .find();
    
    if (appsInCategory.items.length > 0) {
      throw new Error('无法删除包含应用的分类');
    }
    
    const result = await items.removeDataItem('Categories', categoryId);
    return result;
  } catch (error) {
    console.error('Failed to delete category:', error);
    throw error;
  }
}
```

## 5. 管理员界面样式

### 5.1 管理员布局样式
```css
/* styles/admin.css */
.admin-layout {
  display: flex;
  min-height: 100vh;
  background: #f5f5f5;
}

.admin-sidebar {
  width: 250px;
  background: #2c3e50;
  color: white;
  position: fixed;
  height: 100vh;
  overflow-y: auto;
}

.admin-main {
  flex: 1;
  margin-left: 250px;
  display: flex;
  flex-direction: column;
}

.admin-header {
  background: white;
  padding: 1rem 2rem;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.admin-content {
  flex: 1;
  padding: 2rem;
}

/* 仪表板样式 */
.admin-dashboard {
  max-width: 1200px;
}

.dashboard-header {
  margin-bottom: 2rem;
}

.dashboard-header h1 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border-left: 4px solid;
}

.stat-card.blue { border-left-color: #3498db; }
.stat-card.green { border-left-color: #2ecc71; }
.stat-card.orange { border-left-color: #f39c12; }
.stat-card.purple { border-left-color: #9b59b6; }

.stat-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.stat-card-icon {
  font-size: 2rem;
}

.stat-card-value {
  font-size: 2.5rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.stat-card-change {
  font-size: 0.9rem;
  display: flex;
  align-items: center;
}

.stat-card-change.positive { color: #27ae60; }
.stat-card-change.negative { color: #e74c3c; }

.dashboard-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

/* 数据表格样式 */
.data-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.data-table table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background: #f8f9fa;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #2c3e50;
  border-bottom: 1px solid #e0e0e0;
}

.data-table td {
  padding: 1rem;
  border-bottom: 1px solid #f0f0f0;
}

.data-table tr:hover {
  background: #f8f9fa;
}

/* 应用卡片样式 */
.apps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.app-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: transform 0.2s ease;
}

.app-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.app-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.app-card-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.app-card-status {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.app-card-status.pending {
  background: #fff3cd;
  color: #856404;
}

.app-card-status.approved {
  background: #d4edda;
  color: #155724;
}

.app-card-status.rejected {
  background: #f8d7da;
  color: #721c24;
}

.app-card-meta {
  font-size: 0.9rem;
  color: #6c757d;
  margin-bottom: 1rem;
}

.app-card-description {
  color: #495057;
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.app-card-actions {
  display: flex;
  gap: 0.5rem;
}

/* 按钮样式 */
.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover {
  background: #1e7e34;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover {
  background: #c82333;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 8px;
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  margin: 0;
  color: #2c3e50;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6c757d;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  padding: 1.5rem;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-layout {
    flex-direction: column;
  }
  
  .admin-sidebar {
    position: relative;
    width: 100%;
    height: auto;
  }
  
  .admin-main {
    margin-left: 0;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .dashboard-content {
    grid-template-columns: 1fr;
  }
  
  .apps-grid {
    grid-template-columns: 1fr;
  }
}
```

## 6. 权限控制和安全

### 6.1 管理员权限验证
```javascript
// utils/adminAuth.js
import { authentication } from '@wix/members';
import { members } from '@wix/members';

export async function verifyAdminAccess() {
  try {
    const isLoggedIn = await authentication.loggedIn();
    if (!isLoggedIn) {
      throw new Error('用户未登录');
    }
    
    const currentMember = await members.getCurrentMember();
    const userRole = currentMember.customFields?.role;
    
    if (userRole !== 'admin') {
      throw new Error('需要管理员权限');
    }
    
    return currentMember;
  } catch (error) {
    console.error('Admin access verification failed:', error);
    throw error;
  }
}

export function withAdminAuth(handler) {
  return async (...args) => {
    await verifyAdminAccess();
    return handler(...args);
  };
}
```

### 6.2 操作日志记录
```javascript
// utils/auditLog.js
import { items } from '@wix/data';

export async function logAdminAction(action, details, adminId) {
  try {
    await items.insertDataItem('AdminLogs', {
      action,
      details,
      adminId,
      timestamp: new Date(),
      ipAddress: getClientIP()
    });
  } catch (error) {
    console.error('Failed to log admin action:', error);
  }
}

function getClientIP() {
  // 获取客户端 IP 地址的实现
  return 'unknown';
}
```

这个管理员系统提供了完整的后台管理功能，包括用户管理、应用审核、内容管理、数据分析等核心功能。系统设计考虑了权限控制、安全性、用户体验和可扩展性，能够满足应用商店的管理需求。

