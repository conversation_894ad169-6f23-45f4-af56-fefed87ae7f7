<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI App Store - AI应用商店</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card h2 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.5rem;
        }
        
        .api-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .api-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .api-item h3 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .api-item code {
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            color: #d63384;
        }
        
        .status {
            display: inline-block;
            padding: 5px 15px;
            background: #28a745;
            color: white;
            border-radius: 20px;
            font-size: 0.9rem;
            margin-bottom: 20px;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .feature {
            text-align: center;
            padding: 20px;
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 25px;
            transition: background 0.3s ease;
            margin: 10px;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .footer {
            text-align: center;
            color: white;
            margin-top: 40px;
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .container {
                padding: 10px;
            }
            
            .card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 AI App Store</h1>
            <p>AI应用商店后台系统 - 为AI应用提供展示和管理平台</p>
        </div>
        
        <div class="card">
            <div class="status">✅ 系统运行正常</div>
            <h2>🎯 系统概述</h2>
            <p>AI App Store 是一个现代化的应用商店后台系统，专门为展示和管理AI驱动的应用而设计。系统采用Python Flask框架构建，提供完整的RESTful API接口。</p>
            
            <div class="features">
                <div class="feature">
                    <div class="feature-icon">👥</div>
                    <h3>用户管理</h3>
                    <p>完整的用户注册、认证和个人资料管理系统</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">📱</div>
                    <h3>应用展示</h3>
                    <p>支持Vercel部署的AI应用展示和管理</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">🏷️</div>
                    <h3>分类系统</h3>
                    <p>灵活的应用分类和标签管理</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">⭐</div>
                    <h3>评价系统</h3>
                    <p>用户评分和评论功能</p>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h2>🔌 API 接口</h2>
            <p>系统提供完整的RESTful API，支持以下功能模块：</p>
            
            <div class="api-grid">
                <div class="api-item">
                    <h3>👥 用户管理</h3>
                    <p><code>GET /api/users</code> - 获取用户列表</p>
                    <p><code>POST /api/users</code> - 创建用户</p>
                    <p><code>GET /api/users/{id}</code> - 获取用户详情</p>
                </div>
                
                <div class="api-item">
                    <h3>📱 应用管理</h3>
                    <p><code>GET /api/apps</code> - 获取应用列表</p>
                    <p><code>POST /api/apps</code> - 创建应用</p>
                    <p><code>GET /api/apps/{id}</code> - 获取应用详情</p>
                </div>
                
                <div class="api-item">
                    <h3>🏷️ 分类管理</h3>
                    <p><code>GET /api/categories</code> - 获取分类列表</p>
                    <p><code>POST /api/categories</code> - 创建分类</p>
                    <p><code>GET /api/categories/{id}</code> - 获取分类详情</p>
                </div>
                
                <div class="api-item">
                    <h3>⭐ 评论点赞</h3>
                    <p><code>GET /api/apps/{id}/reviews</code> - 获取评论</p>
                    <p><code>POST /api/apps/{id}/reviews</code> - 创建评论</p>
                    <p><code>POST /api/apps/{id}/like</code> - 切换点赞</p>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h2>🛠️ 快速开始</h2>
            <p>开始使用AI App Store API：</p>
            <br>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; font-family: monospace;">
                <p><strong>1. 测试API连接：</strong></p>
                <p>curl <span id="api-url">http://localhost:5000</span>/api/users</p>
                <br>
                <p><strong>2. 创建用户：</strong></p>
                <p>curl -X POST <span id="api-url-2">http://localhost:5000</span>/api/users \<br>
                &nbsp;&nbsp;-H "Content-Type: application/json" \<br>
                &nbsp;&nbsp;-d '{"username": "testuser", "email": "<EMAIL>"}'</p>
            </div>
            
            <div style="margin-top: 20px;">
                <a href="/api/users" class="btn">🔗 测试用户API</a>
                <a href="/api/apps" class="btn">🔗 测试应用API</a>
                <a href="/api/categories" class="btn">🔗 测试分类API</a>
                <a href="/api/apps/stats" class="btn">📊 查看统计</a>
            </div>
        </div>
        
        <div class="card">
            <h2>📚 文档和资源</h2>
            <p>完整的项目文档和资源：</p>
            <ul style="margin-top: 15px; padding-left: 20px;">
                <li><strong>产品需求文档</strong>: docs/Readme_PRD.md</li>
                <li><strong>API接口文档</strong>: docs/Readme_APIs.md</li>
                <li><strong>Windows部署指南</strong>: docs/Deploy_Windows11.md</li>
                <li><strong>Ubuntu部署指南</strong>: docs/Deploy_Ubuntu22.md</li>
                <li><strong>Apifox导入文件</strong>: docs/Apifox_Import.json</li>
            </ul>
        </div>
        
        <div class="footer">
            <p>🤖 Powered by Manus AI | 🚀 Built with Python Flask</p>
            <p>© 2025 AI App Store. All rights reserved.</p>
        </div>
    </div>
    
    <script>
        // 动态设置API URL
        const currentHost = window.location.origin;
        document.getElementById('api-url').textContent = currentHost;
        document.getElementById('api-url-2').textContent = currentHost;
        
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.style.animation = 'fadeInUp 0.6s ease forwards';
            });
        });
        
        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            .card {
                opacity: 0;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>

