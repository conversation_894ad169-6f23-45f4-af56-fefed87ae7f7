from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from src.models.user import db

class Category(db.Model):
    __tablename__ = 'categories'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关系
    apps = db.relationship('App', backref='category', lazy=True)
    
    def __repr__(self):
        return f'<Category {self.name}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'app_count': len(self.apps)
        }

class App(db.Model):
    __tablename__ = 'apps'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    short_description = db.Column(db.String(500))
    vercel_url = db.Column(db.String(500), nullable=False)
    github_url = db.Column(db.String(500))
    icon_url = db.Column(db.String(500))
    screenshots = db.Column(db.JSON)  # 存储截图URL列表
    tags = db.Column(db.JSON)  # 存储标签列表
    ai_framework = db.Column(db.String(100))  # AI框架信息
    tech_stack = db.Column(db.JSON)  # 技术栈
    status = db.Column(db.String(20), default='active')  # active, inactive, pending
    featured = db.Column(db.Boolean, default=False)
    view_count = db.Column(db.Integer, default=0)
    like_count = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 外键
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    category_id = db.Column(db.Integer, db.ForeignKey('categories.id'))
    
    # 关系
    reviews = db.relationship('Review', backref='app', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<App {self.name}>'
    
    def to_dict(self, include_user=True):
        result = {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'short_description': self.short_description,
            'vercel_url': self.vercel_url,
            'github_url': self.github_url,
            'icon_url': self.icon_url,
            'screenshots': self.screenshots or [],
            'tags': self.tags or [],
            'ai_framework': self.ai_framework,
            'tech_stack': self.tech_stack or [],
            'status': self.status,
            'featured': self.featured,
            'view_count': self.view_count,
            'like_count': self.like_count,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'category_id': self.category_id,
            'category': self.category.to_dict() if self.category else None,
            'review_count': len(self.reviews),
            'average_rating': self.get_average_rating()
        }
        
        if include_user and hasattr(self, 'user'):
            result['user'] = {
                'id': self.user.id,
                'username': self.user.username
            }
        
        return result
    
    def get_average_rating(self):
        if not self.reviews:
            return 0
        return sum(review.rating for review in self.reviews) / len(self.reviews)

class Review(db.Model):
    __tablename__ = 'reviews'
    
    id = db.Column(db.Integer, primary_key=True)
    rating = db.Column(db.Integer, nullable=False)  # 1-5星评分
    comment = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 外键
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    app_id = db.Column(db.Integer, db.ForeignKey('apps.id'), nullable=False)
    
    # 约束：每个用户对每个应用只能评论一次
    __table_args__ = (db.UniqueConstraint('user_id', 'app_id', name='unique_user_app_review'),)
    
    def __repr__(self):
        return f'<Review {self.rating} stars for App {self.app_id}>'
    
    def to_dict(self, include_user=True):
        result = {
            'id': self.id,
            'rating': self.rating,
            'comment': self.comment,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'app_id': self.app_id
        }
        
        if include_user and hasattr(self, 'user'):
            result['user'] = {
                'id': self.user.id,
                'username': self.user.username
            }
        
        return result

class AppLike(db.Model):
    __tablename__ = 'app_likes'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    app_id = db.Column(db.Integer, db.ForeignKey('apps.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 约束：每个用户对每个应用只能点赞一次
    __table_args__ = (db.UniqueConstraint('user_id', 'app_id', name='unique_user_app_like'),)
    
    def __repr__(self):
        return f'<AppLike User {self.user_id} likes App {self.app_id}>'

