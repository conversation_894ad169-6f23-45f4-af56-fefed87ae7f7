{"info": {"name": "AI App Store API", "description": "AI应用商店后台API接口文档", "version": "1.0.0", "contact": {"name": "Manus AI", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:5000", "description": "开发环境"}, {"url": "https://api.aiappstore.com", "description": "生产环境"}], "paths": {"/api/users": {"get": {"summary": "获取用户列表", "description": "获取系统中的用户列表，支持分页和搜索", "tags": ["用户管理"], "parameters": [{"name": "page", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "per_page", "in": "query", "description": "每页数量", "required": false, "schema": {"type": "integer", "default": 20}}, {"name": "search", "in": "query", "description": "搜索关键词", "required": false, "schema": {"type": "string"}}, {"name": "include_stats", "in": "query", "description": "是否包含统计信息", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "成功获取用户列表", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserListResponse"}}}}}}, "post": {"summary": "创建用户", "description": "创建新用户账户", "tags": ["用户管理"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserRequest"}}}}, "responses": {"201": {"description": "用户创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/users/{user_id}": {"get": {"summary": "获取用户详情", "description": "获取指定用户的详细信息", "tags": ["用户管理"], "parameters": [{"name": "user_id", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功获取用户详情", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "404": {"description": "用户不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"summary": "更新用户", "description": "更新用户信息", "tags": ["用户管理"], "parameters": [{"name": "user_id", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}}}, "responses": {"200": {"description": "用户更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}}}, "delete": {"summary": "删除用户", "description": "删除用户（软删除）", "tags": ["用户管理"], "parameters": [{"name": "user_id", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "用户删除成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}}}}, "/api/apps": {"get": {"summary": "获取应用列表", "description": "获取应用列表，支持多种筛选和排序选项", "tags": ["应用管理"], "parameters": [{"name": "page", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "per_page", "in": "query", "description": "每页数量", "required": false, "schema": {"type": "integer", "default": 20}}, {"name": "category_id", "in": "query", "description": "分类ID", "required": false, "schema": {"type": "integer"}}, {"name": "featured", "in": "query", "description": "是否推荐应用", "required": false, "schema": {"type": "boolean"}}, {"name": "search", "in": "query", "description": "搜索关键词", "required": false, "schema": {"type": "string"}}, {"name": "sort_by", "in": "query", "description": "排序字段", "required": false, "schema": {"type": "string", "enum": ["created_at", "view_count", "like_count", "rating"], "default": "created_at"}}, {"name": "order", "in": "query", "description": "排序方向", "required": false, "schema": {"type": "string", "enum": ["asc", "desc"], "default": "desc"}}], "responses": {"200": {"description": "成功获取应用列表", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppListResponse"}}}}}}, "post": {"summary": "创建应用", "description": "创建新的应用", "tags": ["应用管理"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAppRequest"}}}}, "responses": {"201": {"description": "应用创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppResponse"}}}}}}}, "/api/apps/{app_id}": {"get": {"summary": "获取应用详情", "description": "获取指定应用的详细信息", "tags": ["应用管理"], "parameters": [{"name": "app_id", "in": "path", "description": "应用ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功获取应用详情", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppResponse"}}}}}}, "put": {"summary": "更新应用", "description": "更新应用信息", "tags": ["应用管理"], "parameters": [{"name": "app_id", "in": "path", "description": "应用ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAppRequest"}}}}, "responses": {"200": {"description": "应用更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppResponse"}}}}}}, "delete": {"summary": "删除应用", "description": "删除指定应用", "tags": ["应用管理"], "parameters": [{"name": "app_id", "in": "path", "description": "应用ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "应用删除成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}}}}, "/api/apps/{app_id}/reviews": {"get": {"summary": "获取应用评论", "description": "获取指定应用的评论列表", "tags": ["评论管理"], "parameters": [{"name": "app_id", "in": "path", "description": "应用ID", "required": true, "schema": {"type": "integer"}}, {"name": "page", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "per_page", "in": "query", "description": "每页数量", "required": false, "schema": {"type": "integer", "default": 10}}], "responses": {"200": {"description": "成功获取评论列表", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReviewListResponse"}}}}}}, "post": {"summary": "创建应用评论", "description": "为指定应用创建评论", "tags": ["评论管理"], "parameters": [{"name": "app_id", "in": "path", "description": "应用ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateReviewRequest"}}}}, "responses": {"201": {"description": "评论创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReviewResponse"}}}}}}}, "/api/apps/{app_id}/like": {"post": {"summary": "切换应用点赞状态", "description": "切换用户对应用的点赞状态", "tags": ["点赞管理"], "parameters": [{"name": "app_id", "in": "path", "description": "应用ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LikeRequest"}}}}, "responses": {"200": {"description": "点赞状态更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LikeResponse"}}}}}}}, "/api/categories": {"get": {"summary": "获取分类列表", "description": "获取所有应用分类", "tags": ["分类管理"], "parameters": [{"name": "include_stats", "in": "query", "description": "是否包含统计信息", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "成功获取分类列表", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryListResponse"}}}}}}, "post": {"summary": "创建分类", "description": "创建新的应用分类", "tags": ["分类管理"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCategoryRequest"}}}}, "responses": {"201": {"description": "分类创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryResponse"}}}}}}}, "/api/apps/stats": {"get": {"summary": "获取应用统计信息", "description": "获取应用相关的统计信息", "tags": ["统计信息"], "responses": {"200": {"description": "成功获取统计信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StatsResponse"}}}}}}}}, "components": {"schemas": {"User": {"type": "object", "properties": {"id": {"type": "integer", "description": "用户ID"}, "username": {"type": "string", "description": "用户名"}, "email": {"type": "string", "description": "邮箱地址"}, "full_name": {"type": "string", "description": "全名"}, "bio": {"type": "string", "description": "个人简介"}, "avatar_url": {"type": "string", "description": "头像链接"}, "github_username": {"type": "string", "description": "GitHub用户名"}, "website_url": {"type": "string", "description": "个人网站"}, "location": {"type": "string", "description": "所在地区"}, "is_verified": {"type": "boolean", "description": "是否认证"}, "is_active": {"type": "boolean", "description": "是否活跃"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间"}, "app_count": {"type": "integer", "description": "应用数量"}, "review_count": {"type": "integer", "description": "评论数量"}, "like_count": {"type": "integer", "description": "点赞数量"}}}, "App": {"type": "object", "properties": {"id": {"type": "integer", "description": "应用ID"}, "name": {"type": "string", "description": "应用名称"}, "description": {"type": "string", "description": "详细描述"}, "short_description": {"type": "string", "description": "简短描述"}, "vercel_url": {"type": "string", "description": "Vercel部署链接"}, "github_url": {"type": "string", "description": "GitHub源码链接"}, "icon_url": {"type": "string", "description": "应用图标链接"}, "screenshots": {"type": "array", "items": {"type": "string"}, "description": "截图链接数组"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "标签数组"}, "ai_framework": {"type": "string", "description": "AI框架信息"}, "tech_stack": {"type": "array", "items": {"type": "string"}, "description": "技术栈数组"}, "status": {"type": "string", "enum": ["active", "inactive", "pending"], "description": "应用状态"}, "featured": {"type": "boolean", "description": "是否推荐"}, "view_count": {"type": "integer", "description": "浏览次数"}, "like_count": {"type": "integer", "description": "点赞次数"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间"}, "category_id": {"type": "integer", "description": "分类ID"}, "category": {"$ref": "#/components/schemas/Category"}, "user": {"type": "object", "properties": {"id": {"type": "integer"}, "username": {"type": "string"}}}, "review_count": {"type": "integer", "description": "评论数量"}, "average_rating": {"type": "number", "description": "平均评分"}}}, "Category": {"type": "object", "properties": {"id": {"type": "integer", "description": "分类ID"}, "name": {"type": "string", "description": "分类名称"}, "description": {"type": "string", "description": "分类描述"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "app_count": {"type": "integer", "description": "应用数量"}}}, "Review": {"type": "object", "properties": {"id": {"type": "integer", "description": "评论ID"}, "rating": {"type": "integer", "minimum": 1, "maximum": 5, "description": "评分（1-5星）"}, "comment": {"type": "string", "description": "评论内容"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "app_id": {"type": "integer", "description": "应用ID"}, "user": {"type": "object", "properties": {"id": {"type": "integer"}, "username": {"type": "string"}}}}}, "Pagination": {"type": "object", "properties": {"page": {"type": "integer", "description": "当前页码"}, "per_page": {"type": "integer", "description": "每页数量"}, "total": {"type": "integer", "description": "总数量"}, "pages": {"type": "integer", "description": "总页数"}, "has_next": {"type": "boolean", "description": "是否有下一页"}, "has_prev": {"type": "boolean", "description": "是否有上一页"}}}, "CreateUserRequest": {"type": "object", "required": ["username", "email"], "properties": {"username": {"type": "string", "description": "用户名"}, "email": {"type": "string", "format": "email", "description": "邮箱地址"}, "full_name": {"type": "string", "description": "全名"}, "bio": {"type": "string", "description": "个人简介"}, "github_username": {"type": "string", "description": "GitHub用户名"}, "website_url": {"type": "string", "description": "个人网站"}, "location": {"type": "string", "description": "所在地区"}}}, "UpdateUserRequest": {"type": "object", "properties": {"username": {"type": "string", "description": "用户名"}, "email": {"type": "string", "format": "email", "description": "邮箱地址"}, "full_name": {"type": "string", "description": "全名"}, "bio": {"type": "string", "description": "个人简介"}, "avatar_url": {"type": "string", "description": "头像链接"}, "github_username": {"type": "string", "description": "GitHub用户名"}, "website_url": {"type": "string", "description": "个人网站"}, "location": {"type": "string", "description": "所在地区"}, "is_verified": {"type": "boolean", "description": "是否认证"}}}, "CreateAppRequest": {"type": "object", "required": ["name", "vercel_url", "user_id"], "properties": {"name": {"type": "string", "description": "应用名称"}, "description": {"type": "string", "description": "详细描述"}, "short_description": {"type": "string", "description": "简短描述"}, "vercel_url": {"type": "string", "description": "Vercel部署链接"}, "github_url": {"type": "string", "description": "GitHub源码链接"}, "icon_url": {"type": "string", "description": "应用图标链接"}, "screenshots": {"type": "array", "items": {"type": "string"}, "description": "截图链接数组"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "标签数组"}, "ai_framework": {"type": "string", "description": "AI框架信息"}, "tech_stack": {"type": "array", "items": {"type": "string"}, "description": "技术栈数组"}, "user_id": {"type": "integer", "description": "创建者用户ID"}, "category_id": {"type": "integer", "description": "分类ID"}}}, "UpdateAppRequest": {"type": "object", "properties": {"name": {"type": "string", "description": "应用名称"}, "description": {"type": "string", "description": "详细描述"}, "short_description": {"type": "string", "description": "简短描述"}, "vercel_url": {"type": "string", "description": "Vercel部署链接"}, "github_url": {"type": "string", "description": "GitHub源码链接"}, "icon_url": {"type": "string", "description": "应用图标链接"}, "screenshots": {"type": "array", "items": {"type": "string"}, "description": "截图链接数组"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "标签数组"}, "ai_framework": {"type": "string", "description": "AI框架信息"}, "tech_stack": {"type": "array", "items": {"type": "string"}, "description": "技术栈数组"}, "category_id": {"type": "integer", "description": "分类ID"}, "status": {"type": "string", "enum": ["active", "inactive", "pending"], "description": "应用状态"}, "featured": {"type": "boolean", "description": "是否推荐"}}}, "CreateCategoryRequest": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "description": "分类名称"}, "description": {"type": "string", "description": "分类描述"}}}, "CreateReviewRequest": {"type": "object", "required": ["rating", "user_id"], "properties": {"rating": {"type": "integer", "minimum": 1, "maximum": 5, "description": "评分（1-5星）"}, "comment": {"type": "string", "description": "评论内容"}, "user_id": {"type": "integer", "description": "评论用户ID"}}}, "LikeRequest": {"type": "object", "required": ["user_id"], "properties": {"user_id": {"type": "integer", "description": "用户ID"}}}, "UserListResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"users": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}, "UserResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"$ref": "#/components/schemas/User"}, "message": {"type": "string"}}}, "AppListResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"apps": {"type": "array", "items": {"$ref": "#/components/schemas/App"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}, "AppResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"$ref": "#/components/schemas/App"}, "message": {"type": "string"}}}, "CategoryListResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Category"}}}}, "CategoryResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"$ref": "#/components/schemas/Category"}, "message": {"type": "string"}}}, "ReviewListResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"reviews": {"type": "array", "items": {"$ref": "#/components/schemas/Review"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}, "ReviewResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"$ref": "#/components/schemas/Review"}, "message": {"type": "string"}}}, "LikeResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"liked": {"type": "boolean", "description": "是否已点赞"}, "like_count": {"type": "integer", "description": "总点赞数"}}}, "message": {"type": "string"}}}, "StatsResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"total_apps": {"type": "integer", "description": "总应用数"}, "featured_apps": {"type": "integer", "description": "推荐应用数"}, "total_views": {"type": "integer", "description": "总浏览数"}, "total_likes": {"type": "integer", "description": "总点赞数"}, "total_reviews": {"type": "integer", "description": "总评论数"}}}}}, "SuccessResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string"}}}, "ErrorResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "error": {"type": "string", "description": "错误信息"}}}}}, "tags": [{"name": "用户管理", "description": "用户相关的API接口"}, {"name": "应用管理", "description": "应用相关的API接口"}, {"name": "分类管理", "description": "分类相关的API接口"}, {"name": "评论管理", "description": "评论相关的API接口"}, {"name": "点赞管理", "description": "点赞相关的API接口"}, {"name": "统计信息", "description": "统计信息相关的API接口"}]}