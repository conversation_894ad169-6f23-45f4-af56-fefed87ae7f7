from flask import Blueprint, request, jsonify
from src.models.user import db, User
from src.models.app import App, Review, AppLike
from sqlalchemy import desc, func
from datetime import datetime

app_bp = Blueprint('app', __name__)

@app_bp.route('/apps', methods=['GET'])
def get_apps():
    """获取应用列表"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        category_id = request.args.get('category_id', type=int)
        featured = request.args.get('featured', type=bool)
        search = request.args.get('search', '')
        sort_by = request.args.get('sort_by', 'created_at')  # created_at, view_count, like_count, rating
        order = request.args.get('order', 'desc')  # asc, desc
        
        # 构建查询
        query = App.query.filter_by(status='active')
        
        if category_id:
            query = query.filter_by(category_id=category_id)
        
        if featured is not None:
            query = query.filter_by(featured=featured)
        
        if search:
            query = query.filter(
                db.or_(
                    App.name.contains(search),
                    App.description.contains(search),
                    App.short_description.contains(search)
                )
            )
        
        # 排序
        if sort_by == 'view_count':
            order_by = App.view_count.desc() if order == 'desc' else App.view_count.asc()
        elif sort_by == 'like_count':
            order_by = App.like_count.desc() if order == 'desc' else App.like_count.asc()
        elif sort_by == 'rating':
            # 按平均评分排序需要子查询
            avg_rating = db.session.query(
                Review.app_id,
                func.avg(Review.rating).label('avg_rating')
            ).group_by(Review.app_id).subquery()
            
            query = query.outerjoin(avg_rating, App.id == avg_rating.c.app_id)
            order_by = avg_rating.c.avg_rating.desc() if order == 'desc' else avg_rating.c.avg_rating.asc()
        else:  # created_at
            order_by = App.created_at.desc() if order == 'desc' else App.created_at.asc()
        
        query = query.order_by(order_by)
        
        # 分页
        pagination = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        apps = [app.to_dict() for app in pagination.items]
        
        return jsonify({
            'success': True,
            'data': {
                'apps': apps,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': pagination.total,
                    'pages': pagination.pages,
                    'has_next': pagination.has_next,
                    'has_prev': pagination.has_prev
                }
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app_bp.route('/apps/<int:app_id>', methods=['GET'])
def get_app(app_id):
    """获取单个应用详情"""
    try:
        app = App.query.get_or_404(app_id)
        
        # 增加浏览次数
        app.view_count += 1
        db.session.commit()
        
        return jsonify({
            'success': True,
            'data': app.to_dict()
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app_bp.route('/apps', methods=['POST'])
def create_app():
    """创建新应用"""
    try:
        data = request.get_json()
        
        # 验证必需字段
        required_fields = ['name', 'vercel_url', 'user_id']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'Missing required field: {field}'}), 400
        
        # 验证用户存在
        user = User.query.get(data['user_id'])
        if not user:
            return jsonify({'success': False, 'error': 'User not found'}), 404
        
        app = App(
            name=data['name'],
            description=data.get('description'),
            short_description=data.get('short_description'),
            vercel_url=data['vercel_url'],
            github_url=data.get('github_url'),
            icon_url=data.get('icon_url'),
            screenshots=data.get('screenshots', []),
            tags=data.get('tags', []),
            ai_framework=data.get('ai_framework'),
            tech_stack=data.get('tech_stack', []),
            user_id=data['user_id'],
            category_id=data.get('category_id')
        )
        
        db.session.add(app)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'data': app.to_dict(),
            'message': 'App created successfully'
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app_bp.route('/apps/<int:app_id>', methods=['PUT'])
def update_app(app_id):
    """更新应用"""
    try:
        app = App.query.get_or_404(app_id)
        data = request.get_json()
        
        # 更新字段
        updatable_fields = [
            'name', 'description', 'short_description', 'vercel_url', 
            'github_url', 'icon_url', 'screenshots', 'tags', 
            'ai_framework', 'tech_stack', 'category_id', 'status', 'featured'
        ]
        
        for field in updatable_fields:
            if field in data:
                setattr(app, field, data[field])
        
        app.updated_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify({
            'success': True,
            'data': app.to_dict(),
            'message': 'App updated successfully'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app_bp.route('/apps/<int:app_id>', methods=['DELETE'])
def delete_app(app_id):
    """删除应用"""
    try:
        app = App.query.get_or_404(app_id)
        db.session.delete(app)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'App deleted successfully'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app_bp.route('/apps/<int:app_id>/reviews', methods=['GET'])
def get_app_reviews(app_id):
    """获取应用评论"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        
        app = App.query.get_or_404(app_id)
        
        pagination = Review.query.filter_by(app_id=app_id)\
            .order_by(Review.created_at.desc())\
            .paginate(page=page, per_page=per_page, error_out=False)
        
        reviews = [review.to_dict() for review in pagination.items]
        
        return jsonify({
            'success': True,
            'data': {
                'reviews': reviews,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': pagination.total,
                    'pages': pagination.pages,
                    'has_next': pagination.has_next,
                    'has_prev': pagination.has_prev
                }
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app_bp.route('/apps/<int:app_id>/reviews', methods=['POST'])
def create_review(app_id):
    """创建应用评论"""
    try:
        data = request.get_json()
        
        # 验证必需字段
        required_fields = ['rating', 'user_id']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'Missing required field: {field}'}), 400
        
        # 验证评分范围
        if not 1 <= data['rating'] <= 5:
            return jsonify({'success': False, 'error': 'Rating must be between 1 and 5'}), 400
        
        # 验证应用和用户存在
        app = App.query.get_or_404(app_id)
        user = User.query.get_or_404(data['user_id'])
        
        # 检查用户是否已经评论过
        existing_review = Review.query.filter_by(
            app_id=app_id, user_id=data['user_id']
        ).first()
        
        if existing_review:
            return jsonify({'success': False, 'error': 'User has already reviewed this app'}), 400
        
        review = Review(
            rating=data['rating'],
            comment=data.get('comment'),
            user_id=data['user_id'],
            app_id=app_id
        )
        
        db.session.add(review)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'data': review.to_dict(),
            'message': 'Review created successfully'
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app_bp.route('/apps/<int:app_id>/like', methods=['POST'])
def toggle_app_like(app_id):
    """切换应用点赞状态"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        
        if not user_id:
            return jsonify({'success': False, 'error': 'Missing user_id'}), 400
        
        # 验证应用和用户存在
        app = App.query.get_or_404(app_id)
        user = User.query.get_or_404(user_id)
        
        # 检查是否已经点赞
        existing_like = AppLike.query.filter_by(
            app_id=app_id, user_id=user_id
        ).first()
        
        if existing_like:
            # 取消点赞
            db.session.delete(existing_like)
            app.like_count = max(0, app.like_count - 1)
            liked = False
        else:
            # 添加点赞
            like = AppLike(app_id=app_id, user_id=user_id)
            db.session.add(like)
            app.like_count += 1
            liked = True
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'data': {
                'liked': liked,
                'like_count': app.like_count
            },
            'message': 'Like status updated successfully'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app_bp.route('/apps/stats', methods=['GET'])
def get_app_stats():
    """获取应用统计信息"""
    try:
        total_apps = App.query.filter_by(status='active').count()
        featured_apps = App.query.filter_by(status='active', featured=True).count()
        total_views = db.session.query(func.sum(App.view_count)).scalar() or 0
        total_likes = db.session.query(func.sum(App.like_count)).scalar() or 0
        total_reviews = Review.query.count()
        
        return jsonify({
            'success': True,
            'data': {
                'total_apps': total_apps,
                'featured_apps': featured_apps,
                'total_views': total_views,
                'total_likes': total_likes,
                'total_reviews': total_reviews
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

