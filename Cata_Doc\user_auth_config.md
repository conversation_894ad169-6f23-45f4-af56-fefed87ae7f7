# 用户认证和权限系统配置

## 1. 系统概述

基于 Wix Members API 和 Site Members 功能，我们将实现以下用户认证和权限系统：

### 用户角色定义
- **游客 (Guest)**: 未登录用户，只能浏览应用
- **注册用户 (Member)**: 已注册并登录的用户，可以上传应用
- **管理员 (Admin)**: 具有管理权限的用户，可以管理所有用户和应用

## 2. 技术实现方案

### 2.1 使用 Wix Members 系统
- 利用已安装的 Site Members 应用
- 使用 Wix Members API 进行用户管理
- 通过自定义字段存储用户角色信息

### 2.2 权限控制实现
```javascript
// 用户权限检查函数
export function checkUserPermission(user, requiredRole) {
  if (!user) return false; // 未登录用户
  
  const userRole = user.customFields?.role || 'member';
  
  switch (requiredRole) {
    case 'guest':
      return true; // 所有用户都可以访问
    case 'member':
      return user.loggedIn; // 需要登录
    case 'admin':
      return userRole === 'admin'; // 需要管理员权限
    default:
      return false;
  }
}
```

## 3. 数据结构设计

### 3.1 用户扩展字段
在 Wix Members 中添加自定义字段：
```javascript
{
  role: 'member' | 'admin',           // 用户角色
  uploadedAppsCount: number,          // 上传应用数量
  lastLoginDate: Date,                // 最后登录时间
  isActive: boolean,                  // 账户状态
  uploadQuota: number                 // 上传配额
}
```

### 3.2 应用数据集合
创建 Apps 数据集合存储应用信息：
```javascript
{
  _id: string,
  title: string,
  description: string,
  category: string,
  downloadUrl: string,
  zipFileUrl: string,
  uploaderId: string,                 // 上传者 Member ID
  uploaderEmail: string,              // 上传者邮箱
  status: 'pending' | 'approved' | 'rejected',
  createdDate: Date,
  updatedDate: Date,
  downloadCount: number,
  fileSize: number,
  version: string,
  tags: string[]
}
```

## 4. 页面权限控制

### 4.1 页面访问权限
```javascript
const pagePermissions = {
  '/': 'guest',                       // 首页 - 所有人可访问
  '/apps/:id': 'guest',              // 应用详情 - 所有人可访问
  '/auth/login': 'guest',            // 登录页 - 所有人可访问
  '/auth/register': 'guest',         // 注册页 - 所有人可访问
  '/dashboard': 'member',            // 用户中心 - 需要登录
  '/dashboard/upload': 'member',     // 上传应用 - 需要登录
  '/dashboard/my-apps': 'member',    // 我的应用 - 需要登录
  '/admin/*': 'admin'                // 管理员页面 - 需要管理员权限
};
```

### 4.2 功能权限控制
```javascript
const featurePermissions = {
  'browse_apps': 'guest',            // 浏览应用
  'view_app_details': 'guest',       // 查看应用详情
  'download_app': 'guest',           // 下载应用
  'upload_app': 'member',            // 上传应用
  'edit_own_app': 'member',          // 编辑自己的应用
  'delete_own_app': 'member',        // 删除自己的应用
  'manage_all_apps': 'admin',        // 管理所有应用
  'manage_users': 'admin',           // 管理用户
  'view_analytics': 'admin'          // 查看统计数据
};
```

## 5. 实现步骤

### 步骤 1: 配置 Members 自定义字段
```javascript
// 在 Wix 后台配置 Members 自定义字段
const memberCustomFields = [
  {
    key: 'role',
    type: 'text',
    displayName: 'User Role',
    defaultValue: 'member'
  },
  {
    key: 'uploadedAppsCount',
    type: 'number',
    displayName: 'Uploaded Apps Count',
    defaultValue: 0
  },
  {
    key: 'uploadQuota',
    type: 'number',
    displayName: 'Upload Quota',
    defaultValue: 10
  },
  {
    key: 'isActive',
    type: 'boolean',
    displayName: 'Account Active',
    defaultValue: true
  }
];
```

### 步骤 2: 创建应用数据集合
```javascript
// 使用 Wix Data API 创建 Apps 集合
import { collections } from 'wix-data.v2';

export async function createAppsCollection() {
  const collection = {
    _id: 'Apps',
    displayName: 'Applications',
    fields: [
      { key: 'title', type: 'text', displayName: 'App Title' },
      { key: 'description', type: 'richText', displayName: 'Description' },
      { key: 'category', type: 'text', displayName: 'Category' },
      { key: 'downloadUrl', type: 'url', displayName: 'Download URL' },
      { key: 'zipFileUrl', type: 'url', displayName: 'ZIP File URL' },
      { key: 'uploaderId', type: 'text', displayName: 'Uploader ID' },
      { key: 'uploaderEmail', type: 'text', displayName: 'Uploader Email' },
      { key: 'status', type: 'text', displayName: 'Status' },
      { key: 'downloadCount', type: 'number', displayName: 'Download Count' },
      { key: 'fileSize', type: 'number', displayName: 'File Size' },
      { key: 'version', type: 'text', displayName: 'Version' },
      { key: 'tags', type: 'multipleValues', displayName: 'Tags' }
    ],
    permissions: {
      insert: 'ADMIN',
      update: 'ADMIN',
      remove: 'ADMIN',
      read: 'ANYONE'
    }
  };
  
  return await collections.createDataCollection(collection);
}
```

### 步骤 3: 实现用户认证中间件
```javascript
// auth-middleware.js
import { authentication } from 'wix-members.v2';
import { members } from 'wix-members.v2';

export async function getCurrentUser() {
  try {
    const isLoggedIn = await authentication.loggedIn();
    if (!isLoggedIn) return null;
    
    const currentMember = await members.getCurrentMember();
    return {
      id: currentMember._id,
      email: currentMember.loginEmail,
      profile: currentMember.profile,
      customFields: currentMember.customFields,
      loggedIn: true
    };
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}

export async function requireAuth(requiredRole = 'member') {
  const user = await getCurrentUser();
  
  if (!user) {
    throw new Error('Authentication required');
  }
  
  if (!checkUserPermission(user, requiredRole)) {
    throw new Error('Insufficient permissions');
  }
  
  return user;
}
```

### 步骤 4: 实现管理员设置
```javascript
// admin-setup.js
import { members } from 'wix-members.v2';

export async function setUserAsAdmin(memberEmail) {
  try {
    const member = await members.queryMembers()
      .eq('loginEmail', memberEmail)
      .find();
    
    if (member.items.length === 0) {
      throw new Error('Member not found');
    }
    
    const memberId = member.items[0]._id;
    
    await members.updateMember(memberId, {
      customFields: {
        role: 'admin'
      }
    });
    
    return { success: true, message: 'User set as admin successfully' };
  } catch (error) {
    console.error('Error setting user as admin:', error);
    throw error;
  }
}
```

## 6. 前端权限控制

### 6.1 React 权限组件
```jsx
// PermissionGuard.jsx
import React from 'react';
import { useCurrentUser } from './hooks/useCurrentUser';
import { checkUserPermission } from './utils/permissions';

export function PermissionGuard({ children, requiredRole, fallback = null }) {
  const { user, loading } = useCurrentUser();
  
  if (loading) {
    return <div>Loading...</div>;
  }
  
  if (!checkUserPermission(user, requiredRole)) {
    return fallback || <div>Access denied</div>;
  }
  
  return children;
}

// 使用示例
<PermissionGuard requiredRole="member">
  <UploadButton />
</PermissionGuard>

<PermissionGuard requiredRole="admin">
  <AdminPanel />
</PermissionGuard>
```

### 6.2 用户状态管理
```jsx
// hooks/useCurrentUser.js
import { useState, useEffect } from 'react';
import { getCurrentUser } from '../utils/auth';

export function useCurrentUser() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    async function loadUser() {
      try {
        const currentUser = await getCurrentUser();
        setUser(currentUser);
      } catch (error) {
        console.error('Error loading user:', error);
      } finally {
        setLoading(false);
      }
    }
    
    loadUser();
  }, []);
  
  return { user, loading, setUser };
}
```

## 7. 安全考虑

### 7.1 前端安全
- 所有敏感操作都需要后端验证
- 前端权限检查仅用于 UI 显示控制
- 使用 HTTPS 传输敏感数据

### 7.2 后端安全
- 所有 API 调用都需要权限验证
- 使用 Wix 的内置安全机制
- 定期审计用户权限

### 7.3 数据安全
- 敏感数据加密存储
- 定期备份用户数据
- 遵循 GDPR 等数据保护法规

## 8. 测试计划

### 8.1 功能测试
- 用户注册和登录流程
- 权限控制功能
- 管理员功能

### 8.2 安全测试
- 权限绕过测试
- 数据泄露测试
- 会话管理测试

### 8.3 性能测试
- 用户认证性能
- 大量用户并发测试
- 数据库查询优化

