import os
import sys
from flask import Flask, send_from_directory, jsonify
from flask_cors import CORS
from dotenv import load_dotenv

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from src.routes.app_routes import app_bp
from src.routes.admin_routes import admin_bp

# 加载环境变量
load_dotenv()

app = Flask(__name__, static_folder=os.path.join(os.path.dirname(__file__), '..', 'static'))
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'your-secret-key-change-in-production')
app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB max file size

# 启用 CORS
CORS(app)

# 注册蓝图
app.register_blueprint(app_bp, url_prefix='/api')
app.register_blueprint(admin_bp, url_prefix='/api/admin')

# 静态文件服务
@app.route('/', defaults={'path': ''})
@app.route('/<path:path>')
def serve(path):
    static_folder_path = app.static_folder
    if static_folder_path is None:
        return "Static folder not configured", 404

    if path != "" and os.path.exists(os.path.join(static_folder_path, path)):
        return send_from_directory(static_folder_path, path)
    else:
        index_path = os.path.join(static_folder_path, 'index.html')
        if os.path.exists(index_path):
            return send_from_directory(static_folder_path, 'index.html')
        else:
            return "index.html not found", 404

# 健康检查端点
@app.route('/api/health')
def health_check():
    return jsonify({
        'status': 'healthy',
        'message': 'App Store API is running',
        'mongodb': 'connected'
    }), 200

# API信息端点
@app.route('/api/info')
def api_info():
    return jsonify({
        'name': 'App Store API',
        'version': '1.0.0',
        'description': 'MongoDB-based App Store Management System',
        'endpoints': {
            'auth': {
                'register': 'POST /api/register',
                'login': 'POST /api/login'
            },
            'apps': {
                'list': 'GET /api/apps',
                'detail': 'GET /api/apps/<id>',
                'create': 'POST /api/apps',
                'update': 'PUT /api/apps/<id>',
                'delete': 'DELETE /api/apps/<id>',
                'download': 'GET /api/apps/<id>/download',
                'files': 'GET /api/apps/<id>/files',
                'my_apps': 'GET /api/my-apps'
            },
            'categories': {
                'list': 'GET /api/categories'
            },
            'admin': {
                'dashboard': 'GET /api/admin/dashboard',
                'apps': 'GET /api/admin/apps',
                'approve_app': 'POST /api/admin/apps/<id>/approve',
                'reject_app': 'POST /api/admin/apps/<id>/reject',
                'feature_app': 'POST /api/admin/apps/<id>/feature',
                'users': 'GET /api/admin/users',
                'system_stats': 'GET /api/admin/system/stats'
            }
        }
    }), 200

if __name__ == '__main__':
    print("🚀 Starting App Store API Server...")
    print(f"📁 Static files: {app.static_folder}")
    print("🔗 MongoDB: Connecting to local instance")
    print("🌐 Server: http://localhost:6101")
    print("📚 API Docs: http://localhost:6101/api/info")
    
    app.run(host='0.0.0.0', port=6101, debug=True)