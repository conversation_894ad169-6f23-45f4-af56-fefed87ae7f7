#!/usr/bin/env python3
"""
最小测试API服务器 - 用于快速验证连接
"""

from flask import Flask, jsonify, request
from flask_cors import CORS
import sys
import os

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

app = Flask(__name__)
CORS(app)

# 模拟用户数据
users = []
categories = [
    {"id": "1", "name": "开发工具", "description": "开发者使用的工具"},
    {"id": "2", "name": "生产力工具", "description": "提高工作效率的工具"},
    {"id": "3", "name": "娱乐应用", "description": "游戏和娱乐相关应用"},
]

@app.route('/api/health', methods=['GET'])
def health_check():
    return jsonify({
        'status': 'healthy',
        'message': 'Test API is running',
        'version': '1.0.0'
    })

@app.route('/api/register', methods=['POST'])
def register():
    try:
        data = request.get_json()
        username = data.get('username')
        email = data.get('email')
        password = data.get('password')
        full_name = data.get('full_name', '')
        
        if not username or not email or not password:
            return jsonify({'message': '用户名、邮箱和密码为必填项'}), 400
            
        # 检查用户是否已存在
        for user in users:
            if user['username'] == username or user['email'] == email:
                return jsonify({'message': '用户名或邮箱已存在'}), 400
        
        # 创建新用户
        new_user = {
            'id': str(len(users) + 1),
            'username': username,
            'email': email,
            'full_name': full_name,
            'role': 'user',
            'is_active': True,
            'created_at': '2025-01-01T00:00:00Z'
        }
        users.append(new_user)
        
        # 移除密码字段返回
        user_response = new_user.copy()
        return jsonify({
            'message': '注册成功',
            'user': user_response,
            'token': 'test-token-' + new_user['id']
        }), 201
        
    except Exception as e:
        return jsonify({'message': f'注册失败: {str(e)}'}), 500

@app.route('/api/login', methods=['POST'])
def login():
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        
        if not username or not password:
            return jsonify({'message': '用户名和密码为必填项'}), 400
        
        # 简单验证（实际应用中应该验证加密密码）
        for user in users:
            if user['username'] == username:
                user_response = user.copy()
                return jsonify({
                    'message': '登录成功',
                    'token': 'test-token-' + user['id'],
                    'user': user_response
                }), 200
        
        # 如果没有找到用户，创建一个默认用户
        if username == 'admin' and password == 'admin123':
            admin_user = {
                'id': 'admin',
                'username': 'admin',
                'email': '<EMAIL>',
                'full_name': '系统管理员',
                'role': 'admin',
                'is_active': True,
                'created_at': '2025-01-01T00:00:00Z'
            }
            users.append(admin_user)
            return jsonify({
                'message': '登录成功',
                'token': 'test-token-admin',
                'user': admin_user
            }), 200
        
        return jsonify({'message': '用户名或密码错误'}), 401
        
    except Exception as e:
        return jsonify({'message': f'登录失败: {str(e)}'}), 500

@app.route('/api/apps', methods=['GET'])
def get_apps():
    sample_apps = [
        {
            'id': '1',
            'title': '示例应用1',
            'description': '这是一个示例应用',
            'url': 'https://example.com',
            'status': 'approved',
            'download_count': 10,
            'view_count': 50,
            'created_at': '2025-01-01T00:00:00Z',
            'user_id': '1'
        },
        {
            'id': '2',
            'title': '示例应用2',
            'description': '这是另一个示例应用',
            'url': 'https://example2.com',
            'status': 'approved',
            'download_count': 5,
            'view_count': 25,
            'created_at': '2025-01-02T00:00:00Z',
            'user_id': '1'
        }
    ]
    
    return jsonify({'apps': sample_apps}), 200

@app.route('/api/categories', methods=['GET'])
def get_categories():
    return jsonify({'categories': categories}), 200

@app.route('/api/my-apps', methods=['GET'])
def get_my_apps():
    # 模拟返回空列表
    return jsonify({'apps': []}), 200

@app.route('/api/info', methods=['GET'])
def api_info():
    return jsonify({
        'name': 'App Store Test API',
        'version': '1.0.0',
        'description': 'Test API for App Store',
        'endpoints': {
            'auth': {
                'register': 'POST /api/register',
                'login': 'POST /api/login'
            },
            'apps': {
                'list': 'GET /api/apps',
                'create': 'POST /api/apps'
            },
            'categories': {
                'list': 'GET /api/categories'
            }
        }
    }), 200

if __name__ == '__main__':
    print("🚀 启动测试API服务器...")
    print("🌐 地址: http://localhost:6101")
    print("📚 文档: http://localhost:6101/api/info")
    print("💡 这是一个测试服务器，数据不会持久化")
    print("🔑 测试账户: admin / admin123")
    
    app.run(host='0.0.0.0', port=6101, debug=True)