from flask import Blueprint, request, jsonify, send_from_directory, current_app
from bson import ObjectId
from ..models.mongodb_models import User, AppStore, Category, mongodb
from ..utils.file_handler import FileHandler
import os
import jwt
from datetime import datetime, timedelta
from functools import wraps

# JWT配置
SECRET_KEY = 'your-secret-key-change-in-production'

def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'message': '缺少认证令牌'}), 401
            
        try:
            token = token.split(' ')[1]  # 移除 'Bearer ' 前缀
            data = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            current_user = User.find_by_id(data['user_id'])
            if not current_user:
                return jsonify({'message': '用户不存在'}), 401
        except:
            return jsonify({'message': '无效的令牌'}), 401
            
        return f(current_user, *args, **kwargs)
    return decorated

def admin_required(f):
    @wraps(f)
    def decorated(current_user, *args, **kwargs):
        if current_user.get('role') != 'admin':
            return jsonify({'message': '需要管理员权限'}), 403
        return f(current_user, *args, **kwargs)
    return decorated

app_bp = Blueprint('app', __name__)
file_handler = FileHandler()

@app_bp.route('/register', methods=['POST'])
def register():
    try:
        data = request.get_json()
        username = data.get('username')
        email = data.get('email')
        password = data.get('password')
        full_name = data.get('full_name')
        
        if not username or not email or not password:
            return jsonify({'message': '用户名、邮箱和密码为必填项'}), 400
            
        user = User.create_user(username, email, password, full_name)
        if user:
            # 移除密码字段
            user.pop('password', None)
            return jsonify({'message': '注册成功', 'user': user}), 201
        else:
            return jsonify({'message': '用户名或邮箱已存在'}), 400
            
    except Exception as e:
        return jsonify({'message': f'注册失败: {str(e)}'}), 500

@app_bp.route('/login', methods=['POST'])
def login():
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        
        if not username or not password:
            return jsonify({'message': '用户名和密码为必填项'}), 400
            
        user = User.find_by_username(username)
        if user and user.get('password') == password:  # 实际应用中应该验证加密密码
            # 生成JWT令牌
            token = jwt.encode({
                'user_id': str(user['_id']),
                'username': user['username'],
                'role': user.get('role', 'user'),
                'exp': datetime.utcnow() + timedelta(days=7)
            }, SECRET_KEY)
            
            user.pop('password', None)
            return jsonify({
                'message': '登录成功',
                'token': token,
                'user': user
            }), 200
        else:
            return jsonify({'message': '用户名或密码错误'}), 401
            
    except Exception as e:
        return jsonify({'message': f'登录失败: {str(e)}'}), 500

@app_bp.route('/apps', methods=['GET'])
def get_apps():
    try:
        status = request.args.get('status', 'approved')
        category_id = request.args.get('category_id')
        limit = int(request.args.get('limit', 50))
        
        query = {}
        if status:
            query['status'] = status
        if category_id:
            query['category_id'] = ObjectId(category_id)
            
        apps_collection = mongodb.get_apps_collection()
        apps = list(apps_collection.find(query).sort('created_at', -1).limit(limit))
        
        # 转换ObjectId为字符串
        for app in apps:
            app['_id'] = str(app['_id'])
            if 'user_id' in app:
                app['user_id'] = str(app['user_id'])
            if 'category_id' in app and app['category_id']:
                app['category_id'] = str(app['category_id'])
                
        return jsonify({'apps': apps}), 200
        
    except Exception as e:
        return jsonify({'message': f'获取应用列表失败: {str(e)}'}), 500

@app_bp.route('/apps/<app_id>', methods=['GET'])
def get_app(app_id):
    try:
        app = AppStore.get_app_by_id(app_id)
        if app:
            app['_id'] = str(app['_id'])
            if 'user_id' in app:
                app['user_id'] = str(app['user_id'])
            if 'category_id' in app and app['category_id']:
                app['category_id'] = str(app['category_id'])
                
            # 增加浏览次数
            AppStore.update_app(app_id, {'view_count': app.get('view_count', 0) + 1})
            
            return jsonify({'app': app}), 200
        else:
            return jsonify({'message': '应用不存在'}), 404
            
    except Exception as e:
        return jsonify({'message': f'获取应用详情失败: {str(e)}'}), 500

@app_bp.route('/apps', methods=['POST'])
@token_required
def create_app(current_user):
    try:
        if 'title' not in request.form or 'url' not in request.form:
            return jsonify({'message': '标题和URL为必填项'}), 400
            
        title = request.form['title']
        url = request.form['url']
        description = request.form.get('description', '')
        category_id = request.form.get('category_id')
        tags = request.form.get('tags', '').split(',') if request.form.get('tags') else []
        
        zip_file = request.files.get('zip_file')
        zip_file_path = None
        extracted_path = None
        
        if zip_file:
            # 保存ZIP文件
            zip_file_path = file_handler.save_uploaded_file(zip_file)
            if zip_file_path:
                # 解压文件
                extracted_path = file_handler.extract_zip_file(zip_file_path)
        
        # 创建应用记录
        app_data = AppStore.create_app(
            title=title,
            url=url,
            zip_file_path=zip_file_path,
            user_id=str(current_user['_id']),
            description=description,
            category_id=category_id,
            tags=[tag.strip() for tag in tags if tag.strip()]
        )
        
        if app_data and extracted_path:
            # 更新解压路径
            AppStore.update_app(str(app_data['_id']), {'extracted_path': extracted_path})
            app_data['extracted_path'] = extracted_path
        
        app_data['_id'] = str(app_data['_id'])
        return jsonify({'message': '应用创建成功', 'app': app_data}), 201
        
    except Exception as e:
        return jsonify({'message': f'创建应用失败: {str(e)}'}), 500

@app_bp.route('/apps/<app_id>', methods=['PUT'])
@token_required
def update_app(current_user, app_id):
    try:
        app = AppStore.get_app_by_id(app_id)
        if not app:
            return jsonify({'message': '应用不存在'}), 404
            
        # 检查权限
        if str(app['user_id']) != str(current_user['_id']) and current_user.get('role') != 'admin':
            return jsonify({'message': '无权限修改此应用'}), 403
            
        data = request.get_json()
        update_data = {}
        
        allowed_fields = ['title', 'url', 'description', 'category_id', 'tags', 'status']
        for field in allowed_fields:
            if field in data:
                update_data[field] = data[field]
                
        if AppStore.update_app(app_id, update_data):
            updated_app = AppStore.get_app_by_id(app_id)
            updated_app['_id'] = str(updated_app['_id'])
            return jsonify({'message': '应用更新成功', 'app': updated_app}), 200
        else:
            return jsonify({'message': '应用更新失败'}), 500
            
    except Exception as e:
        return jsonify({'message': f'更新应用失败: {str(e)}'}), 500

@app_bp.route('/apps/<app_id>', methods=['DELETE'])
@token_required
def delete_app(current_user, app_id):
    try:
        app = AppStore.get_app_by_id(app_id)
        if not app:
            return jsonify({'message': '应用不存在'}), 404
            
        # 检查权限
        if str(app['user_id']) != str(current_user['_id']) and current_user.get('role') != 'admin':
            return jsonify({'message': '无权限删除此应用'}), 403
            
        # 删除文件
        if app.get('zip_file_path'):
            file_handler.delete_file(app['zip_file_path'])
        if app.get('extracted_path'):
            file_handler.delete_file(app['extracted_path'])
            
        if AppStore.delete_app(app_id):
            return jsonify({'message': '应用删除成功'}), 200
        else:
            return jsonify({'message': '应用删除失败'}), 500
            
    except Exception as e:
        return jsonify({'message': f'删除应用失败: {str(e)}'}), 500

@app_bp.route('/apps/<app_id>/download', methods=['GET'])
def download_app(app_id):
    try:
        app = AppStore.get_app_by_id(app_id)
        if not app:
            return jsonify({'message': '应用不存在'}), 404
            
        if app.get('status') != 'approved':
            return jsonify({'message': '应用未通过审核'}), 403
            
        # 增加下载次数
        AppStore.update_app(app_id, {'download_count': app.get('download_count', 0) + 1})
        
        # 重定向到应用URL
        return jsonify({'redirect_url': app['url']}), 200
        
    except Exception as e:
        return jsonify({'message': f'下载应用失败: {str(e)}'}), 500

@app_bp.route('/apps/<app_id>/files', methods=['GET'])
@token_required
def get_app_files(current_user, app_id):
    try:
        app = AppStore.get_app_by_id(app_id)
        if not app:
            return jsonify({'message': '应用不存在'}), 404
            
        # 检查权限
        if str(app['user_id']) != str(current_user['_id']) and current_user.get('role') != 'admin':
            return jsonify({'message': '无权限查看此应用文件'}), 403
            
        extracted_path = app.get('extracted_path')
        if not extracted_path or not os.path.exists(extracted_path):
            return jsonify({'message': '应用文件不存在'}), 404
            
        files = file_handler.get_extracted_files(extracted_path)
        return jsonify({'files': files}), 200
        
    except Exception as e:
        return jsonify({'message': f'获取应用文件失败: {str(e)}'}), 500

@app_bp.route('/categories', methods=['GET'])
def get_categories():
    try:
        categories = Category.get_all_categories()
        for category in categories:
            category['_id'] = str(category['_id'])
        return jsonify({'categories': categories}), 200
        
    except Exception as e:
        return jsonify({'message': f'获取分类列表失败: {str(e)}'}), 500

@app_bp.route('/my-apps', methods=['GET'])
@token_required
def get_my_apps(current_user):
    try:
        apps = AppStore.get_apps_by_user(str(current_user['_id']))
        for app in apps:
            app['_id'] = str(app['_id'])
            if 'category_id' in app and app['category_id']:
                app['category_id'] = str(app['category_id'])
        return jsonify({'apps': apps}), 200
        
    except Exception as e:
        return jsonify({'message': f'获取我的应用失败: {str(e)}'}), 500