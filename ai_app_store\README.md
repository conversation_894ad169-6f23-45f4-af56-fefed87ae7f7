# AI App Store 后台系统

一个符合 Supabase 架构的 AI 应用商店后台系统，使用 Python Flask 构建，为 AI 应用提供展示、管理和分享平台。

## 项目概述

AI App Store 后台系统是一个现代化的 Web 应用，专门为展示和管理 AI 驱动的应用而设计。系统支持用户管理、应用展示、分类管理、评价系统等核心功能，为开发者和用户提供完整的应用生态平台。

### 主要特性

- **用户管理**: 完整的用户注册、认证和个人资料管理
- **应用展示**: 支持 Vercel 部署的 AI 应用展示和管理
- **分类系统**: 灵活的应用分类和标签管理
- **评价系统**: 用户评分和评论功能
- **搜索发现**: 多维度的应用搜索和推荐
- **统计分析**: 详细的应用使用情况和用户行为分析

### 技术栈

- **后端框架**: Python 3.11 + Flask
- **数据库**: SQLite (开发) / PostgreSQL (生产)
- **ORM**: SQLAlchemy
- **API 设计**: RESTful API
- **跨域支持**: Flask-CORS
- **测试框架**: unittest + pytest

## 快速开始

### 环境要求

- Python 3.11 或更高版本
- pip 包管理器
- Git (可选)

### 安装步骤

1. **克隆项目** (或解压 ZIP 文件)
   ```bash
   git clone <repository-url>
   cd ai_app_store
   ```

2. **创建虚拟环境**
   ```bash
   python -m venv venv
   
   # Windows
   venv\Scripts\activate
   
   # Linux/macOS
   source venv/bin/activate
   ```

3. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

4. **运行应用**
   ```bash
   cd src
   python main.py
   ```

5. **访问应用**
   - 应用地址: http://localhost:5000
   - API 文档: 查看 `docs/Readme_APIs.md`

## 项目结构

```
ai_app_store/
├── src/                    # 源代码目录
│   ├── models/            # 数据模型
│   │   ├── user.py       # 用户模型
│   │   └── app.py        # 应用模型
│   ├── routes/           # API 路由
│   │   ├── user.py       # 用户 API
│   │   ├── app.py        # 应用 API
│   │   └── category.py   # 分类 API
│   ├── static/           # 静态文件
│   ├── database/         # 数据库文件
│   └── main.py           # 应用入口
├── tests/                # 测试文件
│   ├── test_api.py       # 单元测试
│   └── curl_tests.sh     # curl 测试脚本
├── docs/                 # 文档目录
│   ├── Readme_PRD.md     # 产品需求文档
│   ├── Readme_APIs.md    # API 接口文档
│   ├── Deploy_Windows11.md  # Windows 部署指南
│   ├── Deploy_Ubuntu22.md   # Ubuntu 部署指南
│   └── Apifox_Import.json   # Apifox 导入文件
├── requirements.txt      # Python 依赖
└── README.md            # 项目说明
```

## API 接口

系统提供完整的 RESTful API，支持以下功能模块：

### 用户管理
- `GET /api/users` - 获取用户列表
- `POST /api/users` - 创建用户
- `GET /api/users/{id}` - 获取用户详情
- `PUT /api/users/{id}` - 更新用户
- `DELETE /api/users/{id}` - 删除用户

### 应用管理
- `GET /api/apps` - 获取应用列表
- `POST /api/apps` - 创建应用
- `GET /api/apps/{id}` - 获取应用详情
- `PUT /api/apps/{id}` - 更新应用
- `DELETE /api/apps/{id}` - 删除应用

### 分类管理
- `GET /api/categories` - 获取分类列表
- `POST /api/categories` - 创建分类
- `GET /api/categories/{id}` - 获取分类详情

### 评论和点赞
- `GET /api/apps/{id}/reviews` - 获取应用评论
- `POST /api/apps/{id}/reviews` - 创建评论
- `POST /api/apps/{id}/like` - 切换点赞状态

详细的 API 文档请查看 `docs/Readme_APIs.md`。

## 测试

### 运行单元测试

```bash
python -m unittest tests.test_api -v
```

### 运行 curl 测试

```bash
# 确保应用正在运行
chmod +x tests/curl_tests.sh
./tests/curl_tests.sh
```

## 部署

项目支持多种部署方式：

### 开发环境
直接运行 Flask 开发服务器：
```bash
python src/main.py
```

### 生产环境
推荐使用 Gunicorn + Nginx：

1. **安装 Gunicorn**
   ```bash
   pip install gunicorn
   ```

2. **运行 Gunicorn**
   ```bash
   gunicorn -c gunicorn.conf.py src.main:app
   ```

3. **配置 Nginx** (可选)
   参考 `docs/Deploy_Ubuntu22.md` 中的 Nginx 配置

### 详细部署指南

- **Windows 11**: 查看 `docs/Deploy_Windows11.md`
- **Ubuntu 22.04**: 查看 `docs/Deploy_Ubuntu22.md`

## 数据库

### 开发环境 (SQLite)
系统默认使用 SQLite 数据库，数据文件位于 `src/database/app.db`。

### 生产环境 (PostgreSQL)
推荐使用 PostgreSQL，配置方法：

1. 安装 PostgreSQL
2. 创建数据库和用户
3. 更新 `src/main.py` 中的数据库连接字符串
4. 安装 `psycopg2-binary` 驱动

## 配置

### 环境变量
创建 `.env` 文件配置环境变量：

```env
FLASK_APP=src/main.py
FLASK_ENV=production
SECRET_KEY=your-secret-key-here
DATABASE_URL=sqlite:///path/to/database.db
```

### 应用配置
主要配置项在 `src/main.py` 中：

- `SECRET_KEY`: 应用密钥
- `SQLALCHEMY_DATABASE_URI`: 数据库连接字符串
- `SQLALCHEMY_TRACK_MODIFICATIONS`: 数据库修改跟踪

## 开发指南

### 添加新的 API 端点

1. 在相应的 `routes/` 文件中添加路由函数
2. 在 `models/` 中定义或修改数据模型
3. 在 `tests/` 中添加相应的测试
4. 更新 API 文档

### 数据库迁移

当修改数据模型时：

1. 删除现有数据库文件 (开发环境)
2. 重新运行应用以创建新表结构
3. 生产环境建议使用 Flask-Migrate

### 代码规范

- 遵循 PEP 8 Python 代码规范
- 使用有意义的变量和函数名
- 添加适当的注释和文档字符串
- 编写单元测试覆盖新功能

## 文档

项目包含完整的文档：

- **产品需求文档**: `docs/Readme_PRD.md`
- **API 接口文档**: `docs/Readme_APIs.md`
- **Windows 部署指南**: `docs/Deploy_Windows11.md`
- **Ubuntu 部署指南**: `docs/Deploy_Ubuntu22.md`
- **Apifox 导入文件**: `docs/Apifox_Import.json`

## 贡献

欢迎贡献代码和建议！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。详情请查看 LICENSE 文件。

## 联系方式

- **作者**: Manus AI
- **邮箱**: <EMAIL>
- **文档**: https://docs.manus.ai
- **GitHub**: https://github.com/manus-ai/ai-app-store

## 更新日志

### v1.0.0 (2025-07-24)
- 初始版本发布
- 实现用户管理、应用管理、分类管理基础功能
- 实现评论和点赞系统
- 提供完整的 RESTful API
- 包含单元测试和部署文档

## 常见问题

### Q: 如何重置数据库？
A: 删除 `src/database/app.db` 文件，重新运行应用即可。

### Q: 如何修改默认端口？
A: 在 `src/main.py` 中修改 `app.run()` 的 `port` 参数。

### Q: 如何启用调试模式？
A: 在 `src/main.py` 中设置 `debug=True`。

### Q: 如何配置 CORS？
A: 系统已默认启用 CORS，支持跨域请求。

更多问题请查看相应的部署文档或联系技术支持。

