/* App Store 样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

/* 按钮样式 */
.btn {
    border-radius: 8px;
    font-weight: 500;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* 首页样式 */
.jumbotron {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
}

.jumbotron h1 {
    font-weight: bold;
}

.jumbotron p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* 应用卡片样式 */
.app-card {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.app-card .card-body {
    flex-grow: 1;
}

.app-card .card-footer {
    background-color: transparent;
    border-top: 1px solid rgba(0,0,0,0.1);
}

/* 状态徽章样式 */
.status-badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
}

/* 表单样式 */
.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #ddd;
    padding: 0.75rem;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* 统计卡片样式 */
.stats-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.stats-card .card-body {
    text-align: center;
    padding: 2rem;
}

.stats-card h5 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stats-card p {
    color: #6c757d;
    margin: 0;
}

/* 模态框样式 */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    border-bottom: 1px solid rgba(0,0,0,0.1);
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid rgba(0,0,0,0.1);
    padding: 1.5rem;
}

/* Toast 通知样式 */
.toast {
    border-radius: 10px;
    border: none;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* 文件上传样式 */
.file-upload-wrapper {
    position: relative;
    display: inline-block;
    cursor: pointer;
}

.file-upload-wrapper input[type=file] {
    position: absolute;
    left: -9999px;
}

.file-upload-label {
    display: inline-block;
    padding: 0.5rem 1rem;
    background-color: #f8f9fa;
    border: 2px dashed #ddd;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.file-upload-label:hover {
    border-color: #667eea;
    background-color: #f0f2ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }
    
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .jumbotron {
        padding: 2rem;
    }
    
    .jumbotron h1 {
        font-size: 2rem;
    }
    
    .stats-card .card-body {
        padding: 1rem;
    }
    
    .stats-card h5 {
        font-size: 2rem;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 分页样式 */
.pagination {
    justify-content: center;
    margin-top: 2rem;
}

.page-link {
    border-radius: 8px;
    margin: 0 0.25rem;
    border: 1px solid #ddd;
    color: #667eea;
}

.page-link:hover {
    background-color: #667eea;
    color: white;
    border-color: #667eea;
}

.page-item.active .page-link {
    background-color: #667eea;
    border-color: #667eea;
}

/* 标签样式 */
.tag {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background-color: #e9ecef;
    border-radius: 20px;
    font-size: 0.875rem;
    margin: 0.25rem;
    color: #495057;
}

.tag:hover {
    background-color: #667eea;
    color: white;
    text-decoration: none;
}

/* 搜索框样式 */
.search-box {
    position: relative;
}

.search-box input {
    padding-left: 2.5rem;
}

.search-box .search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

/* 侧边栏样式 */
.sidebar {
    background-color: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    height: fit-content;
    position: sticky;
    top: 1rem;
}

/* 用户信息样式 */
.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.user-info-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

/* 代码块样式 */
.code-block {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    overflow-x: auto;
}

/* 时间线样式 */
.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -2.5rem;
    top: 0.5rem;
    width: 1rem;
    height: 1rem;
    background-color: #667eea;
    border-radius: 50%;
}