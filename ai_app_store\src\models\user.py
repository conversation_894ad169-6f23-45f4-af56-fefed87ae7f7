from flask_sqlalchemy import SQLAlchemy
from datetime import datetime

db = SQLAlchemy()

class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    full_name = db.Column(db.String(200))
    bio = db.Column(db.Text)
    avatar_url = db.Column(db.String(500))
    github_username = db.Column(db.String(100))
    website_url = db.Column(db.String(500))
    location = db.Column(db.String(200))
    is_verified = db.Column(db.Boolean, default=False)
    is_active = db.Column(db.Bo<PERSON>an, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    apps = db.relationship('App', backref='user', lazy=True)
    reviews = db.relationship('Review', backref='user', lazy=True)
    likes = db.relationship('AppLike', backref='user', lazy=True)

    def __repr__(self):
        return f'<User {self.username}>'

    def to_dict(self, include_stats=False):
        result = {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'full_name': self.full_name,
            'bio': self.bio,
            'avatar_url': self.avatar_url,
            'github_username': self.github_username,
            'website_url': self.website_url,
            'location': self.location,
            'is_verified': self.is_verified,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
        
        if include_stats:
            result.update({
                'app_count': len(self.apps),
                'review_count': len(self.reviews),
                'like_count': len(self.likes)
            })
        
        return result
