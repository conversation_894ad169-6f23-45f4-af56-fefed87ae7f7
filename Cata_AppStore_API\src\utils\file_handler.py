import os
import zipfile
import shutil
from datetime import datetime
import uuid
from werkzeug.utils import secure_filename

class FileHandler:
    def __init__(self, upload_folder='uploads', extracted_folder='extracted'):
        self.upload_folder = upload_folder
        self.extracted_folder = extracted_folder
        self.allowed_extensions = {'zip'}
        
        # 确保目录存在
        os.makedirs(upload_folder, exist_ok=True)
        os.makedirs(extracted_folder, exist_ok=True)
        
    def allowed_file(self, filename):
        return '.' in filename and filename.rsplit('.', 1)[1].lower() in self.allowed_extensions
        
    def save_uploaded_file(self, file):
        """保存上传的文件"""
        if file and self.allowed_file(file.filename):
            # 生成安全的文件名
            filename = secure_filename(file.filename)
            # 添加时间戳避免重名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
            filename = timestamp + filename
            
            file_path = os.path.join(self.upload_folder, filename)
            file.save(file_path)
            return file_path
        return None
        
    def extract_zip_file(self, zip_path, extract_to=None):
        """解压ZIP文件"""
        if not extract_to:
            # 创建唯一的解压目录
            extract_folder = str(uuid.uuid4())
            extract_to = os.path.join(self.extracted_folder, extract_folder)
            
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_to)
            return extract_to
        except Exception as e:
            print(f"解压文件时出错: {e}")
            return None
            
    def get_extracted_files(self, extract_path):
        """获取解压后的文件列表"""
        files = []
        try:
            for root, dirs, filenames in os.walk(extract_path):
                for filename in filenames:
                    file_path = os.path.join(root, filename)
                    relative_path = os.path.relpath(file_path, extract_path)
                    files.append({
                        'name': filename,
                        'path': relative_path,
                        'full_path': file_path,
                        'size': os.path.getsize(file_path)
                    })
        except Exception as e:
            print(f"获取文件列表时出错: {e}")
            
        return files
        
    def delete_file(self, file_path):
        """删除文件"""
        try:
            if os.path.exists(file_path):
                if os.path.isfile(file_path):
                    os.remove(file_path)
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)
                return True
        except Exception as e:
            print(f"删除文件时出错: {e}")
        return False
        
    def get_file_info(self, file_path):
        """获取文件信息"""
        try:
            if os.path.exists(file_path):
                stat = os.stat(file_path)
                return {
                    'name': os.path.basename(file_path),
                    'size': stat.st_size,
                    'created_at': datetime.fromtimestamp(stat.st_ctime),
                    'modified_at': datetime.fromtimestamp(stat.st_mtime),
                    'is_directory': os.path.isdir(file_path)
                }
        except Exception as e:
            print(f"获取文件信息时出错: {e}")
        return None