# AI App Store API 接口文档

**版本**: 1.0  
**作者**: Manus AI  
**创建日期**: 2025年7月24日  
**最后更新**: 2025年7月24日

## 目录

1. [概述](#概述)
2. [认证方式](#认证方式)
3. [响应格式](#响应格式)
4. [错误处理](#错误处理)
5. [用户管理 API](#用户管理-api)
6. [应用管理 API](#应用管理-api)
7. [分类管理 API](#分类管理-api)
8. [评论管理 API](#评论管理-api)
9. [点赞管理 API](#点赞管理-api)
10. [统计信息 API](#统计信息-api)

## 概述

AI App Store API 是一个 RESTful API，为 AI 应用商店提供完整的后端服务。API 支持用户管理、应用展示、分类管理、评价系统等核心功能。

**基础 URL**: `http://localhost:5000/api` (开发环境)

**支持的 HTTP 方法**:
- `GET`: 获取资源
- `POST`: 创建资源
- `PUT`: 更新资源
- `DELETE`: 删除资源

## 认证方式

当前版本暂未实现用户认证，所有接口均为公开访问。在生产环境中建议实现以下认证方式：

- JWT Token 认证
- API Key 认证
- OAuth 2.0 认证

## 响应格式

所有 API 响应均采用统一的 JSON 格式：

### 成功响应
```json
{
  "success": true,
  "data": {
    // 具体数据内容
  },
  "message": "操作成功描述"
}
```

### 错误响应
```json
{
  "success": false,
  "error": "错误信息描述"
}
```

### 分页响应
```json
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "per_page": 20,
      "total": 100,
      "pages": 5,
      "has_next": true,
      "has_prev": false
    }
  }
}
```

## 错误处理

API 使用标准 HTTP 状态码表示请求结果：

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 用户管理 API

### 获取用户列表

**接口**: `GET /users`

**描述**: 获取系统中的用户列表，支持分页和搜索。

**请求参数**:
| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| page | integer | 否 | 1 | 页码 |
| per_page | integer | 否 | 20 | 每页数量 |
| search | string | 否 | - | 搜索关键词 |
| include_stats | boolean | 否 | false | 是否包含统计信息 |

**请求示例**:
```bash
curl -X GET "http://localhost:5000/api/users?page=1&per_page=10&search=test&include_stats=true"
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": 1,
        "username": "testuser1",
        "email": "<EMAIL>",
        "full_name": "Test User 1",
        "bio": "AI开发者",
        "avatar_url": null,
        "github_username": "testuser1",
        "website_url": null,
        "location": "北京",
        "is_verified": false,
        "is_active": true,
        "created_at": "2025-07-24T19:30:00.000Z",
        "updated_at": "2025-07-24T19:30:00.000Z",
        "app_count": 2,
        "review_count": 1,
        "like_count": 3
      }
    ],
    "pagination": {
      "page": 1,
      "per_page": 10,
      "total": 1,
      "pages": 1,
      "has_next": false,
      "has_prev": false
    }
  }
}
```

### 获取用户详情

**接口**: `GET /users/{user_id}`

**描述**: 获取指定用户的详细信息。

**路径参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| user_id | integer | 是 | 用户ID |

**请求示例**:
```bash
curl -X GET "http://localhost:5000/api/users/1"
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "username": "testuser1",
    "email": "<EMAIL>",
    "full_name": "Test User 1",
    "bio": "AI开发者",
    "avatar_url": null,
    "github_username": "testuser1",
    "website_url": null,
    "location": "北京",
    "is_verified": false,
    "is_active": true,
    "created_at": "2025-07-24T19:30:00.000Z",
    "updated_at": "2025-07-24T19:30:00.000Z",
    "app_count": 2,
    "review_count": 1,
    "like_count": 3
  }
}
```

### 创建用户

**接口**: `POST /users`

**描述**: 创建新用户账户。

**请求体**:
```json
{
  "username": "newuser",
  "email": "<EMAIL>",
  "full_name": "New User",
  "bio": "个人简介",
  "github_username": "newuser",
  "website_url": "https://newuser.com",
  "location": "上海"
}
```

**必填字段**:
- `username`: 用户名（唯一）
- `email`: 邮箱地址（唯一）

**请求示例**:
```bash
curl -X POST "http://localhost:5000/api/users" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser",
    "email": "<EMAIL>",
    "full_name": "New User"
  }'
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 2,
    "username": "newuser",
    "email": "<EMAIL>",
    "full_name": "New User",
    "bio": null,
    "avatar_url": null,
    "github_username": null,
    "website_url": null,
    "location": null,
    "is_verified": false,
    "is_active": true,
    "created_at": "2025-07-24T19:35:00.000Z",
    "updated_at": "2025-07-24T19:35:00.000Z"
  },
  "message": "User created successfully"
}
```

### 更新用户

**接口**: `PUT /users/{user_id}`

**描述**: 更新用户信息。

**路径参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| user_id | integer | 是 | 用户ID |

**请求体**:
```json
{
  "full_name": "Updated Name",
  "bio": "更新后的个人简介",
  "location": "深圳"
}
```

**请求示例**:
```bash
curl -X PUT "http://localhost:5000/api/users/1" \
  -H "Content-Type: application/json" \
  -d '{
    "full_name": "Updated Name",
    "bio": "更新后的个人简介"
  }'
```

### 删除用户

**接口**: `DELETE /users/{user_id}`

**描述**: 删除用户（软删除，将用户状态设为非活跃）。

**路径参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| user_id | integer | 是 | 用户ID |

**请求示例**:
```bash
curl -X DELETE "http://localhost:5000/api/users/1"
```

### 获取用户的应用

**接口**: `GET /users/{user_id}/apps`

**描述**: 获取指定用户创建的应用列表。

**路径参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| user_id | integer | 是 | 用户ID |

**请求参数**:
| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| page | integer | 否 | 1 | 页码 |
| per_page | integer | 否 | 20 | 每页数量 |
| status | string | 否 | active | 应用状态 |

**请求示例**:
```bash
curl -X GET "http://localhost:5000/api/users/1/apps?status=active"
```

### 获取用户的评论

**接口**: `GET /users/{user_id}/reviews`

**描述**: 获取指定用户发表的评论列表。

**请求示例**:
```bash
curl -X GET "http://localhost:5000/api/users/1/reviews"
```

### 获取用户点赞的应用

**接口**: `GET /users/{user_id}/likes`

**描述**: 获取指定用户点赞的应用列表。

**请求示例**:
```bash
curl -X GET "http://localhost:5000/api/users/1/likes"
```

## 应用管理 API

### 获取应用列表

**接口**: `GET /apps`

**描述**: 获取应用列表，支持多种筛选和排序选项。

**请求参数**:
| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| page | integer | 否 | 1 | 页码 |
| per_page | integer | 否 | 20 | 每页数量 |
| category_id | integer | 否 | - | 分类ID |
| featured | boolean | 否 | - | 是否推荐应用 |
| search | string | 否 | - | 搜索关键词 |
| sort_by | string | 否 | created_at | 排序字段 |
| order | string | 否 | desc | 排序方向 |

**排序字段选项**:
- `created_at`: 创建时间
- `view_count`: 浏览次数
- `like_count`: 点赞次数
- `rating`: 平均评分

**请求示例**:
```bash
curl -X GET "http://localhost:5000/api/apps?featured=true&sort_by=like_count&order=desc"
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "apps": [
      {
        "id": 1,
        "name": "AI聊天助手",
        "description": "基于GPT的智能聊天助手",
        "short_description": "智能聊天助手",
        "vercel_url": "https://ai-chat-assistant.vercel.app",
        "github_url": "https://github.com/testuser1/ai-chat-assistant",
        "icon_url": "https://example.com/icon1.png",
        "screenshots": ["https://example.com/screenshot1.png"],
        "tags": ["AI", "聊天", "GPT"],
        "ai_framework": "OpenAI GPT-4",
        "tech_stack": ["React", "Next.js", "Python"],
        "status": "active",
        "featured": true,
        "view_count": 150,
        "like_count": 25,
        "created_at": "2025-07-24T19:30:00.000Z",
        "updated_at": "2025-07-24T19:30:00.000Z",
        "category_id": 1,
        "category": {
          "id": 1,
          "name": "AI工具",
          "description": "人工智能相关的应用工具"
        },
        "user": {
          "id": 1,
          "username": "testuser1"
        },
        "review_count": 5,
        "average_rating": 4.6
      }
    ],
    "pagination": {
      "page": 1,
      "per_page": 20,
      "total": 1,
      "pages": 1,
      "has_next": false,
      "has_prev": false
    }
  }
}
```

### 获取应用详情

**接口**: `GET /apps/{app_id}`

**描述**: 获取指定应用的详细信息，会自动增加浏览次数。

**路径参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| app_id | integer | 是 | 应用ID |

**请求示例**:
```bash
curl -X GET "http://localhost:5000/api/apps/1"
```

### 创建应用

**接口**: `POST /apps`

**描述**: 创建新的应用。

**请求体**:
```json
{
  "name": "AI聊天助手",
  "description": "基于GPT的智能聊天助手，支持多种对话模式和个性化设置。",
  "short_description": "智能聊天助手",
  "vercel_url": "https://ai-chat-assistant.vercel.app",
  "github_url": "https://github.com/testuser1/ai-chat-assistant",
  "icon_url": "https://example.com/icon1.png",
  "screenshots": ["https://example.com/screenshot1.png", "https://example.com/screenshot2.png"],
  "tags": ["AI", "聊天", "GPT"],
  "ai_framework": "OpenAI GPT-4",
  "tech_stack": ["React", "Next.js", "Python", "FastAPI"],
  "user_id": 1,
  "category_id": 1
}
```

**必填字段**:
- `name`: 应用名称
- `vercel_url`: Vercel部署链接
- `user_id`: 创建者用户ID

**请求示例**:
```bash
curl -X POST "http://localhost:5000/api/apps" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "AI聊天助手",
    "description": "基于GPT的智能聊天助手",
    "vercel_url": "https://ai-chat-assistant.vercel.app",
    "user_id": 1
  }'
```

### 更新应用

**接口**: `PUT /apps/{app_id}`

**描述**: 更新应用信息。

**路径参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| app_id | integer | 是 | 应用ID |

**请求体**:
```json
{
  "description": "更新后的应用描述",
  "featured": true,
  "status": "active"
}
```

**请求示例**:
```bash
curl -X PUT "http://localhost:5000/api/apps/1" \
  -H "Content-Type: application/json" \
  -d '{
    "description": "更新后的应用描述",
    "featured": true
  }'
```

### 删除应用

**接口**: `DELETE /apps/{app_id}`

**描述**: 删除指定应用。

**路径参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| app_id | integer | 是 | 应用ID |

**请求示例**:
```bash
curl -X DELETE "http://localhost:5000/api/apps/1"
```

## 分类管理 API

### 获取分类列表

**接口**: `GET /categories`

**描述**: 获取所有应用分类。

**请求参数**:
| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| include_stats | boolean | 否 | false | 是否包含统计信息 |

**请求示例**:
```bash
curl -X GET "http://localhost:5000/api/categories?include_stats=true"
```

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "AI工具",
      "description": "人工智能相关的应用工具",
      "created_at": "2025-07-24T19:30:00.000Z",
      "app_count": 5
    },
    {
      "id": 2,
      "name": "Web应用",
      "description": "网页应用程序",
      "created_at": "2025-07-24T19:30:00.000Z",
      "app_count": 3
    }
  ]
}
```

### 获取分类详情

**接口**: `GET /categories/{category_id}`

**描述**: 获取指定分类的详细信息。

**路径参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| category_id | integer | 是 | 分类ID |

**请求示例**:
```bash
curl -X GET "http://localhost:5000/api/categories/1"
```

### 创建分类

**接口**: `POST /categories`

**描述**: 创建新的应用分类。

**请求体**:
```json
{
  "name": "AI工具",
  "description": "人工智能相关的应用工具"
}
```

**必填字段**:
- `name`: 分类名称（唯一）

**请求示例**:
```bash
curl -X POST "http://localhost:5000/api/categories" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "AI工具",
    "description": "人工智能相关的应用工具"
  }'
```

### 更新分类

**接口**: `PUT /categories/{category_id}`

**描述**: 更新分类信息。

**路径参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| category_id | integer | 是 | 分类ID |

**请求体**:
```json
{
  "name": "更新后的分类名",
  "description": "更新后的分类描述"
}
```

**请求示例**:
```bash
curl -X PUT "http://localhost:5000/api/categories/1" \
  -H "Content-Type: application/json" \
  -d '{
    "description": "更新后的分类描述"
  }'
```

### 删除分类

**接口**: `DELETE /categories/{category_id}`

**描述**: 删除指定分类。注意：如果分类下有应用，则无法删除。

**路径参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| category_id | integer | 是 | 分类ID |

**请求示例**:
```bash
curl -X DELETE "http://localhost:5000/api/categories/1"
```

### 获取分类下的应用

**接口**: `GET /categories/{category_id}/apps`

**描述**: 获取指定分类下的应用列表。

**路径参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| category_id | integer | 是 | 分类ID |

**请求参数**:
| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| page | integer | 否 | 1 | 页码 |
| per_page | integer | 否 | 20 | 每页数量 |

**请求示例**:
```bash
curl -X GET "http://localhost:5000/api/categories/1/apps"
```

## 评论管理 API

### 获取应用评论

**接口**: `GET /apps/{app_id}/reviews`

**描述**: 获取指定应用的评论列表。

**路径参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| app_id | integer | 是 | 应用ID |

**请求参数**:
| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| page | integer | 否 | 1 | 页码 |
| per_page | integer | 否 | 10 | 每页数量 |

**请求示例**:
```bash
curl -X GET "http://localhost:5000/api/apps/1/reviews"
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "reviews": [
      {
        "id": 1,
        "rating": 5,
        "comment": "非常棒的应用！界面友好，功能强大。",
        "created_at": "2025-07-24T19:30:00.000Z",
        "app_id": 1,
        "user": {
          "id": 2,
          "username": "testuser2"
        }
      }
    ],
    "pagination": {
      "page": 1,
      "per_page": 10,
      "total": 1,
      "pages": 1,
      "has_next": false,
      "has_prev": false
    }
  }
}
```

### 创建应用评论

**接口**: `POST /apps/{app_id}/reviews`

**描述**: 为指定应用创建评论。每个用户对每个应用只能评论一次。

**路径参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| app_id | integer | 是 | 应用ID |

**请求体**:
```json
{
  "rating": 5,
  "comment": "非常棒的应用！界面友好，功能强大。",
  "user_id": 2
}
```

**必填字段**:
- `rating`: 评分（1-5星）
- `user_id`: 评论用户ID

**请求示例**:
```bash
curl -X POST "http://localhost:5000/api/apps/1/reviews" \
  -H "Content-Type: application/json" \
  -d '{
    "rating": 5,
    "comment": "非常棒的应用！",
    "user_id": 2
  }'
```

## 点赞管理 API

### 切换应用点赞状态

**接口**: `POST /apps/{app_id}/like`

**描述**: 切换用户对应用的点赞状态。如果已点赞则取消，如果未点赞则添加。

**路径参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| app_id | integer | 是 | 应用ID |

**请求体**:
```json
{
  "user_id": 2
}
```

**必填字段**:
- `user_id`: 用户ID

**请求示例**:
```bash
curl -X POST "http://localhost:5000/api/apps/1/like" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 2
  }'
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "liked": true,
    "like_count": 26
  },
  "message": "Like status updated successfully"
}
```

## 统计信息 API

### 获取应用统计信息

**接口**: `GET /apps/stats`

**描述**: 获取应用相关的统计信息。

**请求示例**:
```bash
curl -X GET "http://localhost:5000/api/apps/stats"
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "total_apps": 25,
    "featured_apps": 8,
    "total_views": 1250,
    "total_likes": 180,
    "total_reviews": 45
  }
}
```

## 错误代码说明

### 常见错误代码

| 错误代码 | HTTP状态码 | 说明 |
|----------|------------|------|
| MISSING_FIELD | 400 | 缺少必填字段 |
| INVALID_FORMAT | 400 | 数据格式错误 |
| DUPLICATE_ENTRY | 400 | 数据重复 |
| NOT_FOUND | 404 | 资源不存在 |
| INTERNAL_ERROR | 500 | 服务器内部错误 |

### 错误响应示例

```json
{
  "success": false,
  "error": "Missing required field: name"
}
```

## 使用示例

### 完整的应用创建流程

1. **创建用户**:
```bash
curl -X POST "http://localhost:5000/api/users" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "developer1",
    "email": "<EMAIL>",
    "full_name": "开发者一号"
  }'
```

2. **创建分类**:
```bash
curl -X POST "http://localhost:5000/api/categories" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "AI工具",
    "description": "人工智能相关的应用工具"
  }'
```

3. **创建应用**:
```bash
curl -X POST "http://localhost:5000/api/apps" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "智能写作助手",
    "description": "基于AI的智能写作工具",
    "vercel_url": "https://ai-writer.vercel.app",
    "user_id": 1,
    "category_id": 1
  }'
```

4. **添加评论**:
```bash
curl -X POST "http://localhost:5000/api/apps/1/reviews" \
  -H "Content-Type: application/json" \
  -d '{
    "rating": 5,
    "comment": "很棒的工具！",
    "user_id": 2
  }'
```

5. **点赞应用**:
```bash
curl -X POST "http://localhost:5000/api/apps/1/like" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 2
  }'
```

## 版本更新记录

### v1.0 (2025-07-24)
- 初始版本发布
- 实现用户管理、应用管理、分类管理基础功能
- 实现评论和点赞系统
- 提供完整的 RESTful API

## 联系方式

如有问题或建议，请联系：
- 邮箱: <EMAIL>
- 文档: https://docs.manus.ai
- GitHub: https://github.com/manus-ai/ai-app-store

