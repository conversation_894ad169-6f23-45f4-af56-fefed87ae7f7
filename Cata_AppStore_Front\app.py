import os
import sys
from flask import Flask, send_from_directory, render_template
from flask_cors import CORS

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

app = Flask(__name__, static_folder='static')
app.template_folder = 'templates'

# 启用 CORS
CORS(app)

# 静态文件服务
@app.route('/', defaults={'path': ''})
@app.route('/<path:path>')
def serve(path):
    if path != "" and os.path.exists(os.path.join(app.static_folder, path)):
        return send_from_directory(app.static_folder, path)
    else:
        return render_template('index.html')

# 健康检查端点
@app.route('/health')
def health_check():
    return {'status': 'healthy', 'message': 'Frontend server is running'}

if __name__ == '__main__':
    print("🌐 Starting App Store Frontend Server...")
    print("📁 Static files: " + app.static_folder)
    print("🌐 Server: http://localhost:6021")
    print("📚 Templates: " + app.template_folder)
    
    app.run(host='0.0.0.0', port=6021, debug=True)