#!/usr/bin/env python3
"""
App Store API 初始化脚本
用于创建初始管理员用户和默认分类
"""

import os
import sys
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from src.models.mongodb_models import User, Category

def create_admin_user():
    """创建默认管理员用户"""
    print("🔧 创建默认管理员用户...")
    
    admin_data = User.create_user(
        username='admin',
        email='<EMAIL>',
        password='admin123',  # 生产环境中应该使用更安全的密码
        full_name='系统管理员',
        role='admin'
    )
    
    if admin_data:
        print(f"✅ 管理员用户创建成功: {admin_data['username']}")
        admin_data.pop('password', None)
        print(f"📋 用户信息: {admin_data}")
        return admin_data
    else:
        print("❌ 管理员用户创建失败（可能已存在）")
        return None

def create_default_categories():
    """创建默认分类"""
    print("🔧 创建默认分类...")
    
    default_categories = [
        {
            'name': '开发工具',
            'description': '开发者使用的工具和应用'
        },
        {
            'name': '生产力工具',
            'description': '提高工作效率的工具'
        },
        {
            'name': '娱乐应用',
            'description': '游戏和娱乐相关应用'
        },
        {
            'name': '教育应用',
            'description': '学习和教育相关应用'
        },
        {
            'name': '系统工具',
            'description': '系统维护和管理工具'
        }
    ]
    
    created_categories = []
    
    for category_data in default_categories:
        category = Category.create_category(
            name=category_data['name'],
            description=category_data['description']
        )
        
        if category:
            created_categories.append(category)
            print(f"✅ 分类创建成功: {category['name']}")
        else:
            print(f"❌ 分类创建失败（可能已存在）: {category_data['name']}")
    
    return created_categories

def main():
    """主函数"""
    print("🚀 开始初始化 App Store API...")
    print("=" * 50)
    
    # 创建管理员用户
    admin_user = create_admin_user()
    print()
    
    # 创建默认分类
    categories = create_default_categories()
    print()
    
    # 总结
    print("=" * 50)
    print("✅ 初始化完成!")
    print()
    print("📋 默认账户信息:")
    print("   用户名: admin")
    print("   邮箱: <EMAIL>")
    print("   密码: admin123")
    print("   权限: 管理员")
    print()
    print("📂 已创建分类:")
    for category in categories:
        print(f"   - {category['name']}: {category['description']}")
    print()
    print("⚠️  重要提示:")
    print("   1. 请在生产环境中更改默认密码")
    print("   2. 确保MongoDB服务正在运行")
    print("   3. 使用 'python src/main.py' 启动服务器")

if __name__ == '__main__':
    main()