import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button.jsx'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card.jsx'
import { Badge } from '@/components/ui/badge.jsx'
import { Input } from '@/components/ui/input.jsx'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs.jsx'
import { Search, Star, Heart, ExternalLink, Github, Users, Package, TrendingUp } from 'lucide-react'
import './App.css'

// API 基础地址
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://9000-ixncvei4umbd6nkzcd888-831d6787.manusvm.computer/api'

function App() {
  const [apps, setApps] = useState([])
  const [users, setUsers] = useState([])
  const [categories, setCategories] = useState([])
  const [stats, setStats] = useState({})
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')

  // 获取数据
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        
        // 并行获取所有数据
        const [appsRes, usersRes, categoriesRes, statsRes] = await Promise.all([
          fetch(`${API_BASE_URL}/apps`),
          fetch(`${API_BASE_URL}/users`),
          fetch(`${API_BASE_URL}/categories`),
          fetch(`${API_BASE_URL}/apps/stats`)
        ])

        const [appsData, usersData, categoriesData, statsData] = await Promise.all([
          appsRes.json(),
          usersRes.json(),
          categoriesRes.json(),
          statsRes.json()
        ])

        if (appsData.success) setApps(appsData.data.apps || [])
        if (usersData.success) setUsers(usersData.data.users || [])
        if (categoriesData.success) setCategories(categoriesData.data || [])
        if (statsData.success) setStats(statsData.data || {})
        
      } catch (error) {
        console.error('获取数据失败:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  // 筛选应用
  const filteredApps = apps.filter(app => {
    const matchesSearch = app.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         app.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || 
                           (app.category && app.category.name === selectedCategory)
    return matchesSearch && matchesCategory
  })

  // 点赞应用
  const handleLike = async (appId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/apps/${appId}/like`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ user_id: 1 }) // 示例用户ID
      })
      
      if (response.ok) {
        // 更新本地状态
        setApps(apps.map(app => 
          app.id === appId 
            ? { ...app, like_count: app.like_count + 1 }
            : app
        ))
      }
    } catch (error) {
      console.error('点赞失败:', error)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* 头部 */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
                <Package className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">AI App Store</h1>
                <p className="text-sm text-gray-500">发现优秀的 AI 应用</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  type="text"
                  placeholder="搜索应用..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总应用数</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total_apps || apps.length}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">开发者数量</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{users.length}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总浏览量</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total_views || 0}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总点赞数</CardTitle>
              <Heart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total_likes || 0}</div>
            </CardContent>
          </Card>
        </div>

        {/* 主要内容 */}
        <Tabs defaultValue="apps" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="apps">应用列表</TabsTrigger>
            <TabsTrigger value="developers">开发者</TabsTrigger>
            <TabsTrigger value="categories">分类</TabsTrigger>
          </TabsList>

          {/* 应用列表 */}
          <TabsContent value="apps" className="space-y-6">
            {/* 分类筛选 */}
            <div className="flex flex-wrap gap-2">
              <Button
                variant={selectedCategory === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedCategory('all')}
              >
                全部
              </Button>
              {categories.map((category) => (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.name ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedCategory(category.name)}
                >
                  {category.name}
                </Button>
              ))}
            </div>

            {/* 应用网格 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredApps.length > 0 ? (
                filteredApps.map((app) => (
                  <Card key={app.id} className="hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                            <span className="text-white font-bold text-lg">
                              {app.name.charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <div>
                            <CardTitle className="text-lg">{app.name}</CardTitle>
                            <CardDescription>
                              by {app.user?.username || '未知开发者'}
                            </CardDescription>
                          </div>
                        </div>
                        <Badge variant="secondary">
                          {app.category?.name || '未分类'}
                        </Badge>
                      </div>
                    </CardHeader>
                    
                    <CardContent className="space-y-4">
                      <p className="text-sm text-gray-600 line-clamp-3">
                        {app.description || app.short_description || '暂无描述'}
                      </p>
                      
                      {/* 标签 */}
                      {app.tags && app.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {app.tags.slice(0, 3).map((tag, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      )}
                      
                      {/* 统计信息 */}
                      <div className="flex items-center justify-between text-sm text-gray-500">
                        <div className="flex items-center space-x-4">
                          <span className="flex items-center space-x-1">
                            <Star className="w-4 h-4" />
                            <span>{app.average_rating?.toFixed(1) || '0.0'}</span>
                          </span>
                          <span className="flex items-center space-x-1">
                            <Heart className="w-4 h-4" />
                            <span>{app.like_count || 0}</span>
                          </span>
                        </div>
                        <span>{app.view_count || 0} 次浏览</span>
                      </div>
                      
                      {/* 操作按钮 */}
                      <div className="flex space-x-2">
                        {app.vercel_url && (
                          <Button 
                            size="sm" 
                            className="flex-1"
                            onClick={() => window.open(app.vercel_url, '_blank')}
                          >
                            <ExternalLink className="w-4 h-4 mr-1" />
                            访问应用
                          </Button>
                        )}
                        {app.github_url && (
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => window.open(app.github_url, '_blank')}
                          >
                            <Github className="w-4 h-4" />
                          </Button>
                        )}
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => handleLike(app.id)}
                        >
                          <Heart className="w-4 h-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))
              ) : (
                <div className="col-span-full text-center py-12">
                  <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">暂无应用数据</p>
                  <p className="text-sm text-gray-400 mt-2">
                    {searchTerm ? '尝试调整搜索条件' : '开发者可以通过 API 添加应用'}
                  </p>
                </div>
              )}
            </div>
          </TabsContent>

          {/* 开发者列表 */}
          <TabsContent value="developers" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {users.length > 0 ? (
                users.map((user) => (
                  <Card key={user.id}>
                    <CardHeader>
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center">
                          <span className="text-white font-bold">
                            {user.username.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div>
                          <CardTitle>{user.username}</CardTitle>
                          <CardDescription>{user.email}</CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      {user.bio && (
                        <p className="text-sm text-gray-600 mb-3">{user.bio}</p>
                      )}
                      <div className="flex items-center justify-between text-sm text-gray-500">
                        <span>{user.app_count || 0} 个应用</span>
                        <span>{user.review_count || 0} 条评论</span>
                      </div>
                      {user.github_username && (
                        <Button 
                          size="sm" 
                          variant="outline" 
                          className="w-full mt-3"
                          onClick={() => window.open(`https://github.com/${user.github_username}`, '_blank')}
                        >
                          <Github className="w-4 h-4 mr-1" />
                          GitHub
                        </Button>
                      )}
                    </CardContent>
                  </Card>
                ))
              ) : (
                <div className="col-span-full text-center py-12">
                  <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">暂无开发者数据</p>
                </div>
              )}
            </div>
          </TabsContent>

          {/* 分类列表 */}
          <TabsContent value="categories" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {categories.length > 0 ? (
                categories.map((category) => (
                  <Card key={category.id}>
                    <CardHeader>
                      <CardTitle>{category.name}</CardTitle>
                      <CardDescription>{category.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-blue-600">
                        {apps.filter(app => app.category?.id === category.id).length}
                      </div>
                      <p className="text-sm text-gray-500">个应用</p>
                    </CardContent>
                  </Card>
                ))
              ) : (
                <div className="col-span-full text-center py-12">
                  <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">暂无分类数据</p>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </main>

      {/* 页脚 */}
      <footer className="bg-white border-t mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <p className="text-gray-500">
              © 2025 AI App Store. 由 Manus AI 构建，连接到后台 API: 
              <a 
                href="https://9000-ixncvei4umbd6nkzcd888-831d6787.manusvm.computer" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline ml-1"
              >
                {API_BASE_URL}
              </a>
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default App

