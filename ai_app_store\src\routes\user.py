from flask import Blueprint, request, jsonify
from src.models.user import db, User
from src.models.app import App, Review, AppLike
from datetime import datetime

user_bp = Blueprint('user', __name__)

@user_bp.route('/users', methods=['GET'])
def get_users():
    """获取用户列表"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        search = request.args.get('search', '')
        include_stats = request.args.get('include_stats', False, type=bool)
        
        query = User.query.filter_by(is_active=True)
        
        if search:
            query = query.filter(
                db.or_(
                    User.username.contains(search),
                    User.full_name.contains(search),
                    User.email.contains(search)
                )
            )
        
        pagination = query.order_by(User.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        users = [user.to_dict(include_stats=include_stats) for user in pagination.items]
        
        return jsonify({
            'success': True,
            'data': {
                'users': users,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': pagination.total,
                    'pages': pagination.pages,
                    'has_next': pagination.has_next,
                    'has_prev': pagination.has_prev
                }
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@user_bp.route('/users/<int:user_id>', methods=['GET'])
def get_user(user_id):
    """获取单个用户详情"""
    try:
        user = User.query.get_or_404(user_id)
        
        return jsonify({
            'success': True,
            'data': user.to_dict(include_stats=True)
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@user_bp.route('/users', methods=['POST'])
def create_user():
    """创建新用户"""
    try:
        data = request.get_json()
        
        # 验证必需字段
        required_fields = ['username', 'email']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'Missing required field: {field}'}), 400
        
        # 检查用户名和邮箱是否已存在
        existing_user = User.query.filter(
            db.or_(User.username == data['username'], User.email == data['email'])
        ).first()
        
        if existing_user:
            return jsonify({'success': False, 'error': 'Username or email already exists'}), 400
        
        user = User(
            username=data['username'],
            email=data['email'],
            full_name=data.get('full_name'),
            bio=data.get('bio'),
            avatar_url=data.get('avatar_url'),
            github_username=data.get('github_username'),
            website_url=data.get('website_url'),
            location=data.get('location')
        )
        
        db.session.add(user)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'data': user.to_dict(),
            'message': 'User created successfully'
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@user_bp.route('/users/<int:user_id>', methods=['PUT'])
def update_user(user_id):
    """更新用户"""
    try:
        user = User.query.get_or_404(user_id)
        data = request.get_json()
        
        # 更新字段
        updatable_fields = [
            'username', 'email', 'full_name', 'bio', 'avatar_url',
            'github_username', 'website_url', 'location', 'is_verified'
        ]
        
        for field in updatable_fields:
            if field in data:
                # 检查用户名和邮箱唯一性
                if field in ['username', 'email']:
                    existing_user = User.query.filter(
                        getattr(User, field) == data[field],
                        User.id != user_id
                    ).first()
                    if existing_user:
                        return jsonify({'success': False, 'error': f'{field} already exists'}), 400
                
                setattr(user, field, data[field])
        
        user.updated_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify({
            'success': True,
            'data': user.to_dict(),
            'message': 'User updated successfully'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@user_bp.route('/users/<int:user_id>', methods=['DELETE'])
def delete_user(user_id):
    """删除用户（软删除）"""
    try:
        user = User.query.get_or_404(user_id)
        user.is_active = False
        user.updated_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'User deactivated successfully'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@user_bp.route('/users/<int:user_id>/apps', methods=['GET'])
def get_user_apps(user_id):
    """获取用户的应用"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status', 'active')
        
        user = User.query.get_or_404(user_id)
        
        query = App.query.filter_by(user_id=user_id)
        if status:
            query = query.filter_by(status=status)
        
        pagination = query.order_by(App.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        apps = [app.to_dict(include_user=False) for app in pagination.items]
        
        return jsonify({
            'success': True,
            'data': {
                'user': user.to_dict(),
                'apps': apps,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': pagination.total,
                    'pages': pagination.pages,
                    'has_next': pagination.has_next,
                    'has_prev': pagination.has_prev
                }
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@user_bp.route('/users/<int:user_id>/reviews', methods=['GET'])
def get_user_reviews(user_id):
    """获取用户的评论"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        user = User.query.get_or_404(user_id)
        
        pagination = Review.query.filter_by(user_id=user_id)\
            .order_by(Review.created_at.desc())\
            .paginate(page=page, per_page=per_page, error_out=False)
        
        reviews = [review.to_dict(include_user=False) for review in pagination.items]
        
        return jsonify({
            'success': True,
            'data': {
                'user': user.to_dict(),
                'reviews': reviews,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': pagination.total,
                    'pages': pagination.pages,
                    'has_next': pagination.has_next,
                    'has_prev': pagination.has_prev
                }
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@user_bp.route('/users/<int:user_id>/likes', methods=['GET'])
def get_user_likes(user_id):
    """获取用户点赞的应用"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        user = User.query.get_or_404(user_id)
        
        # 通过 AppLike 表获取用户点赞的应用
        pagination = db.session.query(App).join(AppLike)\
            .filter(AppLike.user_id == user_id)\
            .order_by(AppLike.created_at.desc())\
            .paginate(page=page, per_page=per_page, error_out=False)
        
        apps = [app.to_dict() for app in pagination.items]
        
        return jsonify({
            'success': True,
            'data': {
                'user': user.to_dict(),
                'liked_apps': apps,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': pagination.total,
                    'pages': pagination.pages,
                    'has_next': pagination.has_next,
                    'has_prev': pagination.has_prev
                }
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

