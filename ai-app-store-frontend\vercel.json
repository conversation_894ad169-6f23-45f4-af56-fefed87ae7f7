{"framework": "vite", "buildCommand": "pnpm run build", "outputDirectory": "dist", "installCommand": "pnpm install", "devCommand": "pnpm run dev", "env": {"VITE_API_BASE_URL": "https://9000-ixncvei4umbd6nkzcd888-831d6787.manusvm.computer/api"}, "build": {"env": {"VITE_API_BASE_URL": "https://9000-ixncvei4umbd6nkzcd888-831d6787.manusvm.computer/api"}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}]}], "rewrites": [{"source": "/(.*)", "destination": "/index.html"}]}