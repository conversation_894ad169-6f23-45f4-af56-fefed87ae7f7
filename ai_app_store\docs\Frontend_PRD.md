# AI App Store 前端产品需求文档 (Frontend PRD)

**版本**: 1.0  
**作者**: Manus AI  
**创建日期**: 2025年7月24日  
**最后更新**: 2025年7月24日  
**目标平台**: Vercel 部署  
**技术栈**: React + Next.js + TypeScript

## 目录

1. [产品概述](#产品概述)
2. [技术架构](#技术架构)
3. [功能需求](#功能需求)
4. [用户界面设计](#用户界面设计)
5. [API 对接规范](#api-对接规范)
6. [Vercel 部署配置](#vercel-部署配置)
7. [性能要求](#性能要求)
8. [安全要求](#安全要求)
9. [开发规范](#开发规范)
10. [测试策略](#测试策略)

## 产品概述

### 产品定位

AI App Store 前端是一个现代化的 Web 应用，为用户提供发现、浏览和管理 AI 应用的完整体验。该前端应用将部署在 Vercel 平台上，与后端 Flask API 无缝对接，为开发者和用户构建一个优雅、高效的 AI 应用生态平台。

### 核心价值

- **发现体验**: 为用户提供直观的 AI 应用发现和浏览体验
- **开发者友好**: 为 AI 应用开发者提供便捷的应用发布和管理平台
- **社区互动**: 通过评论、评分、点赞等功能促进用户互动
- **性能优化**: 利用 Vercel 的全球 CDN 和边缘计算能力提供极致性能

### 目标用户

#### 主要用户群体

1. **AI 应用开发者**
   - 需要展示和推广自己开发的 AI 应用
   - 希望获得用户反馈和社区认可
   - 需要管理应用信息和查看使用统计

2. **AI 应用使用者**
   - 寻找实用的 AI 工具和应用
   - 希望了解应用的功能和用户评价
   - 需要便捷的应用访问和使用体验

3. **AI 技术爱好者**
   - 关注 AI 技术发展趋势
   - 希望发现新颖有趣的 AI 应用
   - 乐于分享使用体验和评价

#### 用户画像

**开发者用户 - 张明**
- 年龄: 28岁，前端开发工程师
- 技能: 熟悉 React、Vue.js，对 AI 技术有浓厚兴趣
- 需求: 希望展示自己开发的 AI 应用，获得用户反馈
- 痛点: 缺乏合适的平台展示作品，难以获得用户关注

**使用者用户 - 李华**
- 年龄: 32岁，产品经理
- 背景: 经常需要使用各种工具提高工作效率
- 需求: 寻找能够提高工作效率的 AI 工具
- 痛点: AI 应用分散在各个平台，难以发现和比较

## 技术架构

### 前端技术栈

#### 核心框架
- **React 18**: 现代化的用户界面库，支持并发特性
- **Next.js 14**: 全栈 React 框架，提供 SSR/SSG 支持
- **TypeScript**: 类型安全的 JavaScript 超集
- **Tailwind CSS**: 实用优先的 CSS 框架

#### 状态管理
- **Zustand**: 轻量级状态管理库
- **React Query (TanStack Query)**: 服务端状态管理和缓存

#### UI 组件库
- **Headless UI**: 无样式的可访问组件
- **Radix UI**: 高质量的原始组件
- **Lucide React**: 现代化的图标库

#### 开发工具
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **Husky**: Git hooks 管理
- **Commitlint**: 提交信息规范

### 架构设计

#### 应用架构

```
┌─────────────────────────────────────────────────────────┐
│                    Vercel 部署层                          │
├─────────────────────────────────────────────────────────┤
│                    Next.js 应用层                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐      │
│  │    Pages    │  │ Components  │  │    Hooks    │      │
│  └─────────────┘  └─────────────┘  └─────────────┘      │
├─────────────────────────────────────────────────────────┤
│                    状态管理层                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐      │
│  │   Zustand   │  │ React Query │  │ Local State │      │
│  └─────────────┘  └─────────────┘  └─────────────┘      │
├─────────────────────────────────────────────────────────┤
│                    API 通信层                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐      │
│  │ HTTP Client │  │ API Service │  │ Error Handle│      │
│  └─────────────┘  └─────────────┘  └─────────────┘      │
├─────────────────────────────────────────────────────────┤
│                    后端 API 层                           │
│                 Flask API Server                        │
└─────────────────────────────────────────────────────────┘
```

#### 目录结构

```
ai-app-store-frontend/
├── public/                 # 静态资源
│   ├── icons/             # 应用图标
│   ├── images/            # 图片资源
│   └── favicon.ico        # 网站图标
├── src/
│   ├── components/        # 可复用组件
│   │   ├── ui/           # 基础 UI 组件
│   │   ├── layout/       # 布局组件
│   │   ├── forms/        # 表单组件
│   │   └── features/     # 功能组件
│   ├── pages/            # 页面组件
│   │   ├── api/          # API 路由 (Next.js)
│   │   ├── apps/         # 应用相关页面
│   │   ├── users/        # 用户相关页面
│   │   └── categories/   # 分类相关页面
│   ├── hooks/            # 自定义 Hooks
│   ├── services/         # API 服务
│   ├── stores/           # 状态管理
│   ├── types/            # TypeScript 类型定义
│   ├── utils/            # 工具函数
│   └── styles/           # 样式文件
├── tests/                # 测试文件
├── docs/                 # 文档
├── .env.local            # 环境变量
├── next.config.js        # Next.js 配置
├── tailwind.config.js    # Tailwind 配置
├── tsconfig.json         # TypeScript 配置
├── package.json          # 项目依赖
└── vercel.json           # Vercel 部署配置
```

## 功能需求

### 核心功能模块

#### 1. 首页 (Homepage)

**功能描述**: 应用的主入口页面，展示平台概览和推荐内容。

**具体需求**:
- 展示平台 Logo 和标语
- 推荐应用轮播图 (Carousel)
- 热门应用网格展示
- 最新应用列表
- 分类快速导航
- 搜索框 (全局搜索)
- 用户登录/注册入口

**技术实现**:
- 使用 Next.js SSG 预渲染提高首屏加载速度
- 实现响应式设计，支持移动端和桌面端
- 集成搜索功能，支持实时搜索建议

#### 2. 应用列表页 (Apps Listing)

**功能描述**: 展示所有应用的列表页面，支持多种筛选和排序。

**具体需求**:
- 应用卡片网格布局
- 分页或无限滚动加载
- 筛选功能:
  - 按分类筛选
  - 按标签筛选
  - 按评分筛选
  - 按发布时间筛选
- 排序功能:
  - 最新发布
  - 最受欢迎
  - 评分最高
  - 浏览量最多
- 搜索功能
- 视图切换 (网格/列表)

**应用卡片信息**:
- 应用图标
- 应用名称
- 简短描述
- 开发者信息
- 评分和评论数
- 标签
- 点赞数
- 快速预览按钮

#### 3. 应用详情页 (App Detail)

**功能描述**: 展示单个应用的详细信息和用户交互功能。

**具体需求**:
- 应用基本信息展示:
  - 应用名称和图标
  - 详细描述
  - 开发者信息
  - 技术栈信息
  - 发布时间
  - 更新时间
- 应用截图轮播
- 在线预览/访问按钮
- GitHub 源码链接
- 用户交互功能:
  - 点赞/取消点赞
  - 评分 (1-5星)
  - 评论功能
  - 分享功能
- 相关应用推荐
- 统计信息:
  - 浏览次数
  - 点赞数量
  - 评论数量
  - 平均评分

#### 4. 用户中心 (User Dashboard)

**功能描述**: 用户个人信息管理和应用管理中心。

**具体需求**:
- 用户信息展示和编辑:
  - 头像上传
  - 基本信息编辑
  - 个人简介
  - 社交链接
- 我的应用管理:
  - 已发布应用列表
  - 应用编辑功能
  - 应用统计数据
  - 应用状态管理
- 我的活动:
  - 点赞的应用
  - 评论历史
  - 浏览历史
- 设置页面:
  - 账户设置
  - 隐私设置
  - 通知设置

#### 5. 应用发布/编辑 (App Submission)

**功能描述**: 开发者发布和管理应用的功能页面。

**具体需求**:
- 应用信息表单:
  - 基本信息 (名称、描述、分类)
  - 技术信息 (技术栈、AI框架)
  - 链接信息 (Vercel URL、GitHub)
  - 媒体资源 (图标、截图)
- 表单验证和错误处理
- 草稿保存功能
- 预览功能
- 提交审核流程

#### 6. 分类页面 (Categories)

**功能描述**: 按分类浏览应用的页面。

**具体需求**:
- 分类列表展示
- 每个分类的应用数量统计
- 分类详情页面
- 分类内应用筛选和排序

#### 7. 搜索结果页 (Search Results)

**功能描述**: 展示搜索结果的页面。

**具体需求**:
- 搜索结果列表
- 搜索建议和自动完成
- 高级搜索选项
- 搜索历史
- 无结果时的推荐内容

### 用户认证系统

#### 认证方式
- 邮箱密码注册/登录
- GitHub OAuth 登录
- Google OAuth 登录 (可选)

#### 用户状态管理
- JWT Token 认证
- 自动登录状态保持
- 登录状态过期处理
- 权限控制

## 用户界面设计

### 设计原则

#### 1. 简洁性 (Simplicity)
界面设计遵循简洁原则，避免不必要的装饰元素，让用户专注于核心功能。使用大量留白和清晰的视觉层次，确保信息传达的有效性。

#### 2. 一致性 (Consistency)
在整个应用中保持设计元素的一致性，包括颜色、字体、间距、组件样式等。建立完整的设计系统，确保用户体验的连贯性。

#### 3. 可访问性 (Accessibility)
遵循 WCAG 2.1 AA 标准，确保应用对所有用户都可访问。包括键盘导航支持、屏幕阅读器兼容、色彩对比度要求等。

#### 4. 响应式设计 (Responsive Design)
支持从移动设备到大屏幕的所有设备尺寸，确保在不同设备上都能提供优质的用户体验。

### 视觉设计规范

#### 色彩系统

**主色调 (Primary Colors)**:
- 主色: #667eea (蓝紫色) - 用于主要按钮、链接、强调元素
- 主色变体: #5a6fd8 (深蓝紫) - 用于悬停状态
- 主色浅色: #e0e7ff (浅蓝紫) - 用于背景和辅助元素

**辅助色调 (Secondary Colors)**:
- 成功色: #10b981 (绿色) - 用于成功状态和确认操作
- 警告色: #f59e0b (橙色) - 用于警告信息
- 错误色: #ef4444 (红色) - 用于错误状态和危险操作
- 信息色: #3b82f6 (蓝色) - 用于信息提示

**中性色调 (Neutral Colors)**:
- 文本主色: #111827 (深灰) - 用于主要文本
- 文本辅色: #6b7280 (中灰) - 用于辅助文本
- 边框色: #e5e7eb (浅灰) - 用于边框和分割线
- 背景色: #f9fafb (极浅灰) - 用于页面背景

#### 字体系统

**字体族**:
- 主字体: Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif
- 代码字体: "Fira Code", "Cascadia Code", Consolas, monospace

**字体大小**:
- 标题 1: 2.25rem (36px) - 页面主标题
- 标题 2: 1.875rem (30px) - 章节标题
- 标题 3: 1.5rem (24px) - 子章节标题
- 标题 4: 1.25rem (20px) - 小标题
- 正文: 1rem (16px) - 正文内容
- 小字: 0.875rem (14px) - 辅助信息
- 极小字: 0.75rem (12px) - 标签、状态等

#### 间距系统

基于 8px 网格系统:
- xs: 4px
- sm: 8px
- md: 16px
- lg: 24px
- xl: 32px
- 2xl: 48px
- 3xl: 64px

#### 圆角系统
- 小圆角: 4px - 用于按钮、输入框
- 中圆角: 8px - 用于卡片、模态框
- 大圆角: 12px - 用于大型容器
- 圆形: 50% - 用于头像、图标按钮

### 组件设计规范

#### 按钮 (Buttons)

**主要按钮 (Primary Button)**:
- 背景色: 主色 (#667eea)
- 文字色: 白色
- 悬停状态: 背景色变深 (#5a6fd8)
- 禁用状态: 背景色变浅，文字色变灰

**次要按钮 (Secondary Button)**:
- 背景色: 透明
- 边框: 1px solid 主色
- 文字色: 主色
- 悬停状态: 背景色为主色浅色

**危险按钮 (Danger Button)**:
- 背景色: 错误色 (#ef4444)
- 文字色: 白色
- 用于删除、取消等危险操作

#### 卡片 (Cards)

**应用卡片**:
- 背景: 白色
- 边框: 1px solid #e5e7eb
- 圆角: 12px
- 阴影: 0 1px 3px rgba(0, 0, 0, 0.1)
- 悬停效果: 阴影加深，轻微上移

**内容结构**:
- 应用图标 (64x64px)
- 应用名称 (标题4字体)
- 开发者名称 (小字体)
- 简短描述 (正文字体，最多2行)
- 标签列表
- 评分和统计信息

#### 表单 (Forms)

**输入框 (Input Fields)**:
- 边框: 1px solid #d1d5db
- 圆角: 6px
- 内边距: 12px
- 聚焦状态: 边框色变为主色，添加阴影

**标签 (Labels)**:
- 字体: 正文字体，加粗
- 颜色: 文本主色
- 位置: 输入框上方，8px 间距

**错误状态**:
- 边框色: 错误色
- 错误信息: 错误色文字，输入框下方显示

#### 导航 (Navigation)

**顶部导航栏**:
- 高度: 64px
- 背景: 白色
- 边框底部: 1px solid #e5e7eb
- 内容: Logo、导航链接、搜索框、用户菜单

**侧边导航 (移动端)**:
- 宽度: 280px
- 背景: 白色
- 滑入动画效果

### 页面布局

#### 桌面端布局

**标准页面布局**:
```
┌─────────────────────────────────────────────────────────┐
│                    顶部导航栏 (64px)                      │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  ┌─────────────┐  ┌─────────────────────────────────┐   │
│  │             │  │                                 │   │
│  │  侧边栏     │  │         主内容区域               │   │
│  │  (240px)    │  │                                 │   │
│  │             │  │                                 │   │
│  └─────────────┘  └─────────────────────────────────┘   │
│                                                         │
├─────────────────────────────────────────────────────────┤
│                    页脚 (120px)                          │
└─────────────────────────────────────────────────────────┘
```

#### 移动端布局

**移动端适配**:
- 隐藏侧边栏，使用汉堡菜单
- 导航栏高度调整为 56px
- 主内容区域全宽显示
- 底部添加标签栏导航 (可选)

### 交互设计

#### 微交互

**悬停效果**:
- 按钮: 背景色变化 + 轻微缩放 (scale: 1.02)
- 卡片: 阴影加深 + 上移 (translateY: -2px)
- 链接: 颜色变化 + 下划线

**点击反馈**:
- 按钮: 轻微缩放 (scale: 0.98)
- 卡片: 快速缩放动画

**加载状态**:
- 骨架屏 (Skeleton) 用于内容加载
- 旋转加载器用于操作反馈
- 进度条用于文件上传

#### 动画效果

**页面转场**:
- 淡入淡出 (fade in/out)
- 滑动效果 (slide)
- 持续时间: 200-300ms

**元素动画**:
- 弹性动画 (spring) 用于模态框
- 缓动函数: ease-out
- 避免过度动画，保持性能

## API 对接规范

### API 基础配置

#### 环境配置

**开发环境**:
```typescript
const API_CONFIG = {
  baseURL: 'http://localhost:5000',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  }
}
```

**生产环境**:
```typescript
const API_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json',
  }
}
```

#### HTTP 客户端配置

使用 Axios 作为 HTTP 客户端，配置拦截器处理认证和错误:

```typescript
import axios from 'axios'

const apiClient = axios.create(API_CONFIG)

// 请求拦截器 - 添加认证头
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('auth_token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// 响应拦截器 - 统一错误处理
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // 处理认证失效
      localStorage.removeItem('auth_token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)
```

### API 服务层设计

#### 基础 API 服务类

```typescript
class BaseApiService {
  protected baseURL: string
  
  constructor(baseURL: string) {
    this.baseURL = baseURL
  }
  
  protected async request<T>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    endpoint: string,
    data?: any,
    params?: any
  ): Promise<T> {
    const response = await apiClient.request({
      method,
      url: `${this.baseURL}${endpoint}`,
      data,
      params
    })
    return response.data
  }
}
```

#### 用户 API 服务

```typescript
interface User {
  id: number
  username: string
  email: string
  full_name?: string
  bio?: string
  avatar_url?: string
  github_username?: string
  website_url?: string
  location?: string
  is_verified: boolean
  is_active: boolean
  created_at: string
  updated_at: string
  app_count?: number
  review_count?: number
  like_count?: number
}

interface CreateUserRequest {
  username: string
  email: string
  full_name?: string
  bio?: string
  github_username?: string
  website_url?: string
  location?: string
}

interface UpdateUserRequest {
  username?: string
  email?: string
  full_name?: string
  bio?: string
  avatar_url?: string
  github_username?: string
  website_url?: string
  location?: string
}

class UserApiService extends BaseApiService {
  constructor() {
    super('/api/users')
  }
  
  async getUsers(params?: {
    page?: number
    per_page?: number
    search?: string
    include_stats?: boolean
  }): Promise<{
    success: boolean
    data: {
      users: User[]
      pagination: PaginationInfo
    }
  }> {
    return this.request('GET', '', undefined, params)
  }
  
  async createUser(data: CreateUserRequest): Promise<{
    success: boolean
    data: User
    message: string
  }> {
    return this.request('POST', '', data)
  }
  
  async getUserById(id: number): Promise<{
    success: boolean
    data: User
  }> {
    return this.request('GET', `/${id}`)
  }
  
  async updateUser(id: number, data: UpdateUserRequest): Promise<{
    success: boolean
    data: User
    message: string
  }> {
    return this.request('PUT', `/${id}`, data)
  }
  
  async deleteUser(id: number): Promise<{
    success: boolean
    message: string
  }> {
    return this.request('DELETE', `/${id}`)
  }
}
```

#### 应用 API 服务

```typescript
interface App {
  id: number
  name: string
  description: string
  short_description?: string
  vercel_url: string
  github_url?: string
  icon_url?: string
  screenshots: string[]
  tags: string[]
  ai_framework?: string
  tech_stack: string[]
  status: 'active' | 'inactive' | 'pending'
  featured: boolean
  view_count: number
  like_count: number
  created_at: string
  updated_at: string
  category_id?: number
  category?: Category
  user: {
    id: number
    username: string
  }
  review_count: number
  average_rating: number
}

interface CreateAppRequest {
  name: string
  description: string
  short_description?: string
  vercel_url: string
  github_url?: string
  icon_url?: string
  screenshots?: string[]
  tags?: string[]
  ai_framework?: string
  tech_stack?: string[]
  user_id: number
  category_id?: number
}

class AppApiService extends BaseApiService {
  constructor() {
    super('/api/apps')
  }
  
  async getApps(params?: {
    page?: number
    per_page?: number
    category_id?: number
    featured?: boolean
    search?: string
    sort_by?: 'created_at' | 'view_count' | 'like_count' | 'rating'
    order?: 'asc' | 'desc'
  }): Promise<{
    success: boolean
    data: {
      apps: App[]
      pagination: PaginationInfo
    }
  }> {
    return this.request('GET', '', undefined, params)
  }
  
  async createApp(data: CreateAppRequest): Promise<{
    success: boolean
    data: App
    message: string
  }> {
    return this.request('POST', '', data)
  }
  
  async getAppById(id: number): Promise<{
    success: boolean
    data: App
  }> {
    return this.request('GET', `/${id}`)
  }
  
  async updateApp(id: number, data: Partial<CreateAppRequest>): Promise<{
    success: boolean
    data: App
    message: string
  }> {
    return this.request('PUT', `/${id}`, data)
  }
  
  async deleteApp(id: number): Promise<{
    success: boolean
    message: string
  }> {
    return this.request('DELETE', `/${id}`)
  }
  
  async likeApp(id: number, userId: number): Promise<{
    success: boolean
    data: {
      liked: boolean
      like_count: number
    }
    message: string
  }> {
    return this.request('POST', `/${id}/like`, { user_id: userId })
  }
  
  async getAppReviews(id: number, params?: {
    page?: number
    per_page?: number
  }): Promise<{
    success: boolean
    data: {
      reviews: Review[]
      pagination: PaginationInfo
    }
  }> {
    return this.request('GET', `/${id}/reviews`, undefined, params)
  }
  
  async createAppReview(id: number, data: {
    rating: number
    comment?: string
    user_id: number
  }): Promise<{
    success: boolean
    data: Review
    message: string
  }> {
    return this.request('POST', `/${id}/reviews`, data)
  }
  
  async getAppStats(): Promise<{
    success: boolean
    data: {
      total_apps: number
      featured_apps: number
      total_views: number
      total_likes: number
      total_reviews: number
    }
  }> {
    return this.request('GET', '/stats')
  }
}
```

### React Query 集成

#### 查询 Hooks

```typescript
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

// 获取应用列表
export const useApps = (params?: GetAppsParams) => {
  return useQuery({
    queryKey: ['apps', params],
    queryFn: () => appApiService.getApps(params),
    staleTime: 5 * 60 * 1000, // 5分钟
    cacheTime: 10 * 60 * 1000, // 10分钟
  })
}

// 获取应用详情
export const useApp = (id: number) => {
  return useQuery({
    queryKey: ['app', id],
    queryFn: () => appApiService.getAppById(id),
    enabled: !!id,
  })
}

// 创建应用
export const useCreateApp = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: appApiService.createApp,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['apps'] })
    },
  })
}

// 点赞应用
export const useLikeApp = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, userId }: { id: number; userId: number }) =>
      appApiService.likeApp(id, userId),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['app', variables.id] })
      queryClient.invalidateQueries({ queryKey: ['apps'] })
    },
  })
}
```

### 错误处理

#### 错误类型定义

```typescript
interface ApiError {
  success: false
  error: string
  details?: any
}

interface ValidationError {
  field: string
  message: string
}
```

#### 全局错误处理

```typescript
import { toast } from 'react-hot-toast'

export const handleApiError = (error: any) => {
  if (error.response?.data?.error) {
    toast.error(error.response.data.error)
  } else if (error.message) {
    toast.error(error.message)
  } else {
    toast.error('发生未知错误，请稍后重试')
  }
}

// 在组件中使用
const { mutate: createApp, isLoading } = useCreateApp({
  onError: handleApiError,
  onSuccess: () => {
    toast.success('应用创建成功')
    router.push('/dashboard')
  }
})
```

## Vercel 部署配置

### 项目配置

#### package.json 配置

```json
{
  "name": "ai-app-store-frontend",
  "version": "1.0.0",
  "private": true,
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "type-check": "tsc --noEmit",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage"
  },
  "dependencies": {
    "next": "^14.0.0",
    "react": "^18.0.0",
    "react-dom": "^18.0.0",
    "typescript": "^5.0.0",
    "@tanstack/react-query": "^5.0.0",
    "axios": "^1.6.0",
    "zustand": "^4.4.0",
    "tailwindcss": "^3.3.0",
    "@headlessui/react": "^1.7.0",
    "@radix-ui/react-dialog": "^1.0.0",
    "lucide-react": "^0.300.0",
    "react-hook-form": "^7.48.0",
    "@hookform/resolvers": "^3.3.0",
    "zod": "^3.22.0",
    "react-hot-toast": "^2.4.0",
    "framer-motion": "^10.16.0"
  },
  "devDependencies": {
    "@types/node": "^20.0.0",
    "@types/react": "^18.0.0",
    "@types/react-dom": "^18.0.0",
    "eslint": "^8.0.0",
    "eslint-config-next": "^14.0.0",
    "prettier": "^3.0.0",
    "jest": "^29.0.0",
    "@testing-library/react": "^13.0.0",
    "@testing-library/jest-dom": "^6.0.0",
    "husky": "^8.0.0",
    "lint-staged": "^15.0.0"
  }
}
```

#### next.config.js 配置

```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  images: {
    domains: [
      'localhost',
      'your-api-domain.com',
      'github.com',
      'avatars.githubusercontent.com',
      'vercel.app',
    ],
    formats: ['image/webp', 'image/avif'],
  },
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
  },
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `${process.env.NEXT_PUBLIC_API_URL}/api/:path*`,
      },
    ]
  },
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
    ]
  },
}

module.exports = nextConfig
```

#### vercel.json 配置

```json
{
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "outputDirectory": ".next",
  "env": {
    "NEXT_PUBLIC_API_URL": "@api-url",
    "NEXT_PUBLIC_APP_URL": "@app-url"
  },
  "build": {
    "env": {
      "NEXT_PUBLIC_API_URL": "@api-url",
      "NEXT_PUBLIC_APP_URL": "@app-url"
    }
  },
  "functions": {
    "pages/api/**/*.js": {
      "runtime": "nodejs18.x"
    }
  },
  "headers": [
    {
      "source": "/api/(.*)",
      "headers": [
        {
          "key": "Access-Control-Allow-Origin",
          "value": "*"
        },
        {
          "key": "Access-Control-Allow-Methods",
          "value": "GET, POST, PUT, DELETE, OPTIONS"
        },
        {
          "key": "Access-Control-Allow-Headers",
          "value": "Content-Type, Authorization"
        }
      ]
    }
  ],
  "redirects": [
    {
      "source": "/home",
      "destination": "/",
      "permanent": true
    }
  ],
  "rewrites": [
    {
      "source": "/sitemap.xml",
      "destination": "/api/sitemap"
    }
  ]
}
```

### 环境变量配置

#### 开发环境 (.env.local)

```env
# API 配置
NEXT_PUBLIC_API_URL=http://localhost:5000
NEXT_PUBLIC_APP_URL=http://localhost:3000

# 认证配置
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key

# GitHub OAuth
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# Google OAuth (可选)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# 分析工具 (可选)
NEXT_PUBLIC_GA_ID=your-google-analytics-id
```

#### 生产环境 (Vercel 环境变量)

在 Vercel 控制台中配置以下环境变量:

- `NEXT_PUBLIC_API_URL`: 生产环境 API 地址
- `NEXT_PUBLIC_APP_URL`: 生产环境应用地址
- `NEXTAUTH_URL`: 生产环境应用地址
- `NEXTAUTH_SECRET`: 随机生成的密钥
- `GITHUB_CLIENT_ID`: GitHub OAuth 客户端 ID
- `GITHUB_CLIENT_SECRET`: GitHub OAuth 客户端密钥

### 部署流程

#### 自动部署配置

1. **连接 GitHub 仓库**:
   - 在 Vercel 控制台导入 GitHub 仓库
   - 配置自动部署分支 (通常是 main 或 master)

2. **构建配置**:
   - Framework Preset: Next.js
   - Build Command: `npm run build`
   - Output Directory: `.next`
   - Install Command: `npm install`

3. **域名配置**:
   - 使用 Vercel 提供的默认域名
   - 或配置自定义域名

#### 部署脚本

```bash
#!/bin/bash
# deploy.sh

echo "开始部署 AI App Store 前端..."

# 检查环境变量
if [ -z "$NEXT_PUBLIC_API_URL" ]; then
  echo "错误: NEXT_PUBLIC_API_URL 环境变量未设置"
  exit 1
fi

# 安装依赖
echo "安装依赖..."
npm ci

# 类型检查
echo "执行类型检查..."
npm run type-check

# 运行测试
echo "运行测试..."
npm run test

# 构建应用
echo "构建应用..."
npm run build

echo "部署完成!"
```

### 性能优化

#### 构建优化

```javascript
// next.config.js 中的优化配置
const nextConfig = {
  // 启用 SWC 编译器
  swcMinify: true,
  
  // 压缩配置
  compress: true,
  
  // 图片优化
  images: {
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
  },
  
  // 实验性功能
  experimental: {
    // 启用 App Router
    appDir: true,
    // 启用服务端组件
    serverComponents: true,
    // 启用并发特性
    concurrentFeatures: true,
  },
  
  // Webpack 配置
  webpack: (config, { dev, isServer }) => {
    // 生产环境优化
    if (!dev && !isServer) {
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
        },
      }
    }
    
    return config
  },
}
```

#### 代码分割

```typescript
// 动态导入组件
import dynamic from 'next/dynamic'

const AppEditor = dynamic(() => import('../components/AppEditor'), {
  loading: () => <div>加载中...</div>,
  ssr: false,
})

// 路由级别的代码分割
const DashboardPage = dynamic(() => import('../pages/dashboard'))
```

#### 缓存策略

```typescript
// API 缓存配置
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5分钟
      cacheTime: 10 * 60 * 1000, // 10分钟
      retry: 3,
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
  },
})

// 静态资源缓存
// vercel.json 中配置
{
  "headers": [
    {
      "source": "/static/(.*)",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, max-age=31536000, immutable"
        }
      ]
    }
  ]
}
```

## 性能要求

### 性能指标

#### Core Web Vitals

**Largest Contentful Paint (LCP)**:
- 目标: < 2.5秒
- 优化策略:
  - 使用 Next.js Image 组件优化图片加载
  - 实施关键资源预加载
  - 优化服务端渲染

**First Input Delay (FID)**:
- 目标: < 100毫秒
- 优化策略:
  - 减少 JavaScript 执行时间
  - 使用 Web Workers 处理重计算
  - 优化事件处理器

**Cumulative Layout Shift (CLS)**:
- 目标: < 0.1
- 优化策略:
  - 为图片和视频设置尺寸属性
  - 避免在现有内容上方插入内容
  - 使用骨架屏减少布局偏移

#### 其他性能指标

**首屏加载时间**:
- 目标: < 3秒 (3G 网络)
- 目标: < 1.5秒 (4G 网络)

**页面切换时间**:
- 目标: < 200毫秒

**API 响应时间**:
- 目标: < 500毫秒 (95th percentile)

### 性能优化策略

#### 前端优化

**代码分割**:
```typescript
// 路由级别分割
const HomePage = lazy(() => import('./pages/HomePage'))
const AppDetailPage = lazy(() => import('./pages/AppDetailPage'))

// 组件级别分割
const HeavyComponent = lazy(() => import('./components/HeavyComponent'))

// 使用 Suspense 包装
<Suspense fallback={<LoadingSpinner />}>
  <HeavyComponent />
</Suspense>
```

**资源预加载**:
```typescript
// 关键路由预加载
import { useRouter } from 'next/router'

const router = useRouter()

// 预加载重要页面
useEffect(() => {
  router.prefetch('/apps')
  router.prefetch('/dashboard')
}, [router])

// 图片预加载
<link rel="preload" as="image" href="/hero-image.webp" />
```

**虚拟滚动**:
```typescript
import { FixedSizeList as List } from 'react-window'

const AppList = ({ apps }) => (
  <List
    height={600}
    itemCount={apps.length}
    itemSize={120}
    itemData={apps}
  >
    {AppListItem}
  </List>
)
```

#### 图片优化

**Next.js Image 组件**:
```typescript
import Image from 'next/image'

const AppIcon = ({ src, alt }) => (
  <Image
    src={src}
    alt={alt}
    width={64}
    height={64}
    placeholder="blur"
    blurDataURL="data:image/jpeg;base64,..."
    priority={false}
  />
)
```

**响应式图片**:
```typescript
const AppScreenshot = ({ src, alt }) => (
  <Image
    src={src}
    alt={alt}
    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
    fill
    style={{ objectFit: 'cover' }}
  />
)
```

#### 缓存优化

**浏览器缓存**:
```typescript
// Service Worker 缓存策略
const CACHE_NAME = 'ai-app-store-v1'
const urlsToCache = [
  '/',
  '/static/css/main.css',
  '/static/js/main.js',
]

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(urlsToCache))
  )
})
```

**API 缓存**:
```typescript
// React Query 缓存配置
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5分钟
      cacheTime: 10 * 60 * 1000, // 10分钟
    },
  },
})

// 特定查询的缓存策略
export const useApps = (params) => {
  return useQuery({
    queryKey: ['apps', params],
    queryFn: () => fetchApps(params),
    staleTime: 2 * 60 * 1000, // 2分钟
    cacheTime: 5 * 60 * 1000, // 5分钟
  })
}
```

### 监控和分析

#### 性能监控

**Web Vitals 监控**:
```typescript
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals'

function sendToAnalytics(metric) {
  // 发送到分析服务
  gtag('event', metric.name, {
    value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
    event_category: 'Web Vitals',
    event_label: metric.id,
    non_interaction: true,
  })
}

getCLS(sendToAnalytics)
getFID(sendToAnalytics)
getFCP(sendToAnalytics)
getLCP(sendToAnalytics)
getTTFB(sendToAnalytics)
```

**错误监控**:
```typescript
// 全局错误处理
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error)
  // 发送错误报告
})

window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason)
  // 发送错误报告
})

// React 错误边界
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error) {
    return { hasError: true }
  }

  componentDidCatch(error, errorInfo) {
    console.error('React error boundary:', error, errorInfo)
    // 发送错误报告
  }

  render() {
    if (this.state.hasError) {
      return <ErrorFallback />
    }

    return this.props.children
  }
}
```

## 安全要求

### 前端安全

#### 内容安全策略 (CSP)

```typescript
// next.config.js 中配置 CSP
const ContentSecurityPolicy = `
  default-src 'self';
  script-src 'self' 'unsafe-eval' 'unsafe-inline' *.vercel.app;
  child-src *.vercel.app;
  style-src 'self' 'unsafe-inline' *.googleapis.com;
  img-src * blob: data:;
  media-src 'none';
  connect-src *;
  font-src 'self' *.googleapis.com *.gstatic.com;
`

const securityHeaders = [
  {
    key: 'Content-Security-Policy',
    value: ContentSecurityPolicy.replace(/\n/g, ''),
  },
  {
    key: 'Referrer-Policy',
    value: 'origin-when-cross-origin',
  },
  {
    key: 'X-Frame-Options',
    value: 'DENY',
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff',
  },
  {
    key: 'X-DNS-Prefetch-Control',
    value: 'false',
  },
  {
    key: 'Strict-Transport-Security',
    value: 'max-age=31536000; includeSubDomains; preload',
  },
  {
    key: 'Permissions-Policy',
    value: 'camera=(), microphone=(), geolocation=()',
  },
]
```

#### 输入验证和清理

```typescript
import DOMPurify from 'dompurify'
import { z } from 'zod'

// 表单验证 schema
const createAppSchema = z.object({
  name: z.string().min(1, '应用名称不能为空').max(100, '应用名称过长'),
  description: z.string().min(10, '描述至少10个字符').max(1000, '描述过长'),
  vercel_url: z.string().url('请输入有效的URL'),
  github_url: z.string().url('请输入有效的GitHub URL').optional(),
  tags: z.array(z.string()).max(10, '标签数量不能超过10个'),
})

// HTML 内容清理
const sanitizeHtml = (html: string) => {
  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'ul', 'ol', 'li'],
    ALLOWED_ATTR: [],
  })
}

// URL 验证
const isValidUrl = (url: string) => {
  try {
    const urlObj = new URL(url)
    return ['http:', 'https:'].includes(urlObj.protocol)
  } catch {
    return false
  }
}
```

#### 认证和授权

```typescript
// JWT Token 处理
class AuthService {
  private tokenKey = 'auth_token'
  
  setToken(token: string) {
    localStorage.setItem(this.tokenKey, token)
  }
  
  getToken(): string | null {
    return localStorage.getItem(this.tokenKey)
  }
  
  removeToken() {
    localStorage.removeItem(this.tokenKey)
  }
  
  isTokenValid(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]))
      return payload.exp > Date.now() / 1000
    } catch {
      return false
    }
  }
  
  getCurrentUser(): User | null {
    const token = this.getToken()
    if (!token || !this.isTokenValid(token)) {
      return null
    }
    
    try {
      const payload = JSON.parse(atob(token.split('.')[1]))
      return payload.user
    } catch {
      return null
    }
  }
}

// 路由保护
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { user, isLoading } = useAuth()
  const router = useRouter()
  
  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/login')
    }
  }, [user, isLoading, router])
  
  if (isLoading) {
    return <LoadingSpinner />
  }
  
  if (!user) {
    return null
  }
  
  return <>{children}</>
}
```

#### 数据保护

```typescript
// 敏感数据处理
const maskEmail = (email: string) => {
  const [username, domain] = email.split('@')
  const maskedUsername = username.slice(0, 2) + '*'.repeat(username.length - 2)
  return `${maskedUsername}@${domain}`
}

// 防止 XSS 攻击
const escapeHtml = (text: string) => {
  const map: { [key: string]: string } = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#039;',
  }
  
  return text.replace(/[&<>"']/g, (m) => map[m])
}

// 安全的本地存储
class SecureStorage {
  private encrypt(data: string): string {
    // 简单的加密实现，生产环境应使用更强的加密
    return btoa(data)
  }
  
  private decrypt(data: string): string {
    try {
      return atob(data)
    } catch {
      return ''
    }
  }
  
  setItem(key: string, value: string) {
    localStorage.setItem(key, this.encrypt(value))
  }
  
  getItem(key: string): string | null {
    const item = localStorage.getItem(key)
    return item ? this.decrypt(item) : null
  }
  
  removeItem(key: string) {
    localStorage.removeItem(key)
  }
}
```

### API 安全

#### HTTPS 强制

```typescript
// 强制 HTTPS 重定向
const enforceHttps = () => {
  if (typeof window !== 'undefined' && window.location.protocol === 'http:') {
    window.location.href = window.location.href.replace('http:', 'https:')
  }
}

// 在应用启动时调用
useEffect(() => {
  if (process.env.NODE_ENV === 'production') {
    enforceHttps()
  }
}, [])
```

#### API 请求安全

```typescript
// API 请求拦截器
apiClient.interceptors.request.use((config) => {
  // 添加 CSRF Token
  const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
  if (csrfToken) {
    config.headers['X-CSRF-Token'] = csrfToken
  }
  
  // 添加请求时间戳防止重放攻击
  config.headers['X-Timestamp'] = Date.now().toString()
  
  return config
})

// 请求签名验证 (可选)
const generateSignature = (data: any, timestamp: string, secret: string) => {
  const payload = JSON.stringify(data) + timestamp
  // 使用 HMAC-SHA256 生成签名
  return crypto.subtle.digest('SHA-256', new TextEncoder().encode(payload + secret))
}
```

## 开发规范

### 代码规范

#### TypeScript 配置

```json
{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"],
      "@/pages/*": ["./src/pages/*"],
      "@/hooks/*": ["./src/hooks/*"],
      "@/services/*": ["./src/services/*"],
      "@/types/*": ["./src/types/*"],
      "@/utils/*": ["./src/utils/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
```

#### ESLint 配置

```json
{
  "extends": [
    "next/core-web-vitals",
    "@typescript-eslint/recommended",
    "prettier"
  ],
  "parser": "@typescript-eslint/parser",
  "plugins": ["@typescript-eslint"],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/no-explicit-any": "warn",
    "@typescript-eslint/explicit-function-return-type": "off",
    "@typescript-eslint/explicit-module-boundary-types": "off",
    "react/prop-types": "off",
    "react/react-in-jsx-scope": "off",
    "prefer-const": "error",
    "no-var": "error"
  }
}
```

#### Prettier 配置

```json
{
  "semi": false,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false
}
```

### 组件开发规范

#### 组件结构

```typescript
// components/AppCard/AppCard.tsx
import React from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { App } from '@/types/app'
import { StarIcon, HeartIcon } from 'lucide-react'
import styles from './AppCard.module.css'

interface AppCardProps {
  app: App
  onLike?: (appId: number) => void
  className?: string
}

export const AppCard: React.FC<AppCardProps> = ({
  app,
  onLike,
  className = '',
}) => {
  const handleLike = (e: React.MouseEvent) => {
    e.preventDefault()
    onLike?.(app.id)
  }

  return (
    <Link href={`/apps/${app.id}`} className={`${styles.card} ${className}`}>
      <div className={styles.header}>
        <Image
          src={app.icon_url || '/default-app-icon.png'}
          alt={`${app.name} icon`}
          width={64}
          height={64}
          className={styles.icon}
        />
        <div className={styles.info}>
          <h3 className={styles.title}>{app.name}</h3>
          <p className={styles.developer}>by {app.user.username}</p>
        </div>
      </div>
      
      <p className={styles.description}>{app.short_description}</p>
      
      <div className={styles.tags}>
        {app.tags.slice(0, 3).map((tag) => (
          <span key={tag} className={styles.tag}>
            {tag}
          </span>
        ))}
      </div>
      
      <div className={styles.footer}>
        <div className={styles.rating}>
          <StarIcon size={16} />
          <span>{app.average_rating.toFixed(1)}</span>
        </div>
        <button
          onClick={handleLike}
          className={styles.likeButton}
          aria-label="点赞"
        >
          <HeartIcon size={16} />
          <span>{app.like_count}</span>
        </button>
      </div>
    </Link>
  )
}

export default AppCard
```

#### 自定义 Hook 规范

```typescript
// hooks/useApps.ts
import { useState, useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import { appApiService } from '@/services/api'
import { App, GetAppsParams } from '@/types/app'

interface UseAppsOptions {
  initialParams?: GetAppsParams
  enabled?: boolean
}

export const useApps = (options: UseAppsOptions = {}) => {
  const { initialParams = {}, enabled = true } = options
  const [params, setParams] = useState<GetAppsParams>(initialParams)

  const query = useQuery({
    queryKey: ['apps', params],
    queryFn: () => appApiService.getApps(params),
    enabled,
    staleTime: 5 * 60 * 1000, // 5分钟
  })

  const updateParams = (newParams: Partial<GetAppsParams>) => {
    setParams(prev => ({ ...prev, ...newParams }))
  }

  const resetParams = () => {
    setParams(initialParams)
  }

  return {
    ...query,
    params,
    updateParams,
    resetParams,
    apps: query.data?.data.apps || [],
    pagination: query.data?.data.pagination,
  }
}

// 使用示例
const AppsPage = () => {
  const {
    apps,
    pagination,
    isLoading,
    error,
    updateParams,
  } = useApps({
    initialParams: { page: 1, per_page: 20 }
  })

  const handlePageChange = (page: number) => {
    updateParams({ page })
  }

  if (isLoading) return <LoadingSpinner />
  if (error) return <ErrorMessage error={error} />

  return (
    <div>
      <AppGrid apps={apps} />
      <Pagination
        current={pagination?.page || 1}
        total={pagination?.pages || 1}
        onChange={handlePageChange}
      />
    </div>
  )
}
```

### 状态管理规范

#### Zustand Store 设计

```typescript
// stores/authStore.ts
import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { User } from '@/types/user'
import { authApiService } from '@/services/api'

interface AuthState {
  user: User | null
  token: string | null
  isLoading: boolean
  error: string | null
}

interface AuthActions {
  login: (email: string, password: string) => Promise<void>
  logout: () => void
  updateUser: (userData: Partial<User>) => void
  clearError: () => void
}

type AuthStore = AuthState & AuthActions

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // State
      user: null,
      token: null,
      isLoading: false,
      error: null,

      // Actions
      login: async (email: string, password: string) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await authApiService.login({ email, password })
          set({
            user: response.data.user,
            token: response.data.token,
            isLoading: false,
          })
        } catch (error) {
          set({
            error: error.message || '登录失败',
            isLoading: false,
          })
        }
      },

      logout: () => {
        set({
          user: null,
          token: null,
          error: null,
        })
      },

      updateUser: (userData: Partial<User>) => {
        const currentUser = get().user
        if (currentUser) {
          set({
            user: { ...currentUser, ...userData },
          })
        }
      },

      clearError: () => {
        set({ error: null })
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
      }),
    }
  )
)

// 使用示例
const LoginForm = () => {
  const { login, isLoading, error, clearError } = useAuthStore()
  
  const handleSubmit = async (data: LoginFormData) => {
    await login(data.email, data.password)
  }

  useEffect(() => {
    return () => clearError()
  }, [clearError])

  return (
    <form onSubmit={handleSubmit}>
      {/* 表单内容 */}
    </form>
  )
}
```

### 测试规范

#### 单元测试

```typescript
// __tests__/components/AppCard.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { AppCard } from '@/components/AppCard'
import { mockApp } from '@/tests/mocks/app'

describe('AppCard', () => {
  const defaultProps = {
    app: mockApp,
    onLike: jest.fn(),
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders app information correctly', () => {
    render(<AppCard {...defaultProps} />)
    
    expect(screen.getByText(mockApp.name)).toBeInTheDocument()
    expect(screen.getByText(`by ${mockApp.user.username}`)).toBeInTheDocument()
    expect(screen.getByText(mockApp.short_description)).toBeInTheDocument()
  })

  it('calls onLike when like button is clicked', () => {
    render(<AppCard {...defaultProps} />)
    
    const likeButton = screen.getByLabelText('点赞')
    fireEvent.click(likeButton)
    
    expect(defaultProps.onLike).toHaveBeenCalledWith(mockApp.id)
  })

  it('displays correct rating', () => {
    render(<AppCard {...defaultProps} />)
    
    expect(screen.getByText(mockApp.average_rating.toFixed(1))).toBeInTheDocument()
  })

  it('renders tags correctly', () => {
    render(<AppCard {...defaultProps} />)
    
    mockApp.tags.slice(0, 3).forEach(tag => {
      expect(screen.getByText(tag)).toBeInTheDocument()
    })
  })
})
```

#### 集成测试

```typescript
// __tests__/pages/apps.test.tsx
import { render, screen, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import AppsPage from '@/pages/apps'
import { mockApps } from '@/tests/mocks/app'
import * as api from '@/services/api'

jest.mock('@/services/api')
const mockedApi = api as jest.Mocked<typeof api>

const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

const renderWithQueryClient = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient()
  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  )
}

describe('AppsPage', () => {
  beforeEach(() => {
    mockedApi.appApiService.getApps.mockResolvedValue({
      success: true,
      data: {
        apps: mockApps,
        pagination: {
          page: 1,
          per_page: 20,
          total: mockApps.length,
          pages: 1,
          has_next: false,
          has_prev: false,
        },
      },
    })
  })

  it('renders apps list', async () => {
    renderWithQueryClient(<AppsPage />)
    
    await waitFor(() => {
      mockApps.forEach(app => {
        expect(screen.getByText(app.name)).toBeInTheDocument()
      })
    })
  })

  it('shows loading state initially', () => {
    renderWithQueryClient(<AppsPage />)
    
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
  })

  it('handles API error', async () => {
    mockedApi.appApiService.getApps.mockRejectedValue(new Error('API Error'))
    
    renderWithQueryClient(<AppsPage />)
    
    await waitFor(() => {
      expect(screen.getByText(/发生错误/)).toBeInTheDocument()
    })
  })
})
```

## 测试策略

### 测试金字塔

#### 单元测试 (70%)

**覆盖范围**:
- 工具函数
- 自定义 Hooks
- 组件逻辑
- 状态管理

**测试工具**:
- Jest: 测试运行器和断言库
- React Testing Library: React 组件测试
- MSW: API 模拟

**示例**:
```typescript
// utils/formatDate.test.ts
import { formatDate, formatRelativeTime } from './formatDate'

describe('formatDate', () => {
  it('formats date correctly', () => {
    const date = new Date('2023-12-25T10:30:00Z')
    expect(formatDate(date)).toBe('2023年12月25日')
  })

  it('handles invalid date', () => {
    expect(formatDate(null)).toBe('无效日期')
  })
})

describe('formatRelativeTime', () => {
  it('returns correct relative time', () => {
    const now = new Date()
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)
    
    expect(formatRelativeTime(oneHourAgo)).toBe('1小时前')
  })
})
```

#### 集成测试 (20%)

**覆盖范围**:
- 页面组件
- API 集成
- 用户流程

**测试策略**:
```typescript
// __tests__/integration/app-submission.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { AppSubmissionPage } from '@/pages/apps/submit'
import { TestWrapper } from '@/tests/utils/TestWrapper'

describe('App Submission Flow', () => {
  it('allows user to submit new app', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <AppSubmissionPage />
      </TestWrapper>
    )

    // 填写表单
    await user.type(screen.getByLabelText('应用名称'), 'Test App')
    await user.type(screen.getByLabelText('应用描述'), 'This is a test app')
    await user.type(screen.getByLabelText('Vercel URL'), 'https://test-app.vercel.app')

    // 提交表单
    await user.click(screen.getByRole('button', { name: '提交应用' }))

    // 验证成功消息
    await waitFor(() => {
      expect(screen.getByText('应用提交成功')).toBeInTheDocument()
    })
  })

  it('shows validation errors for invalid input', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <AppSubmissionPage />
      </TestWrapper>
    )

    // 提交空表单
    await user.click(screen.getByRole('button', { name: '提交应用' }))

    // 验证错误消息
    await waitFor(() => {
      expect(screen.getByText('应用名称不能为空')).toBeInTheDocument()
      expect(screen.getByText('应用描述不能为空')).toBeInTheDocument()
    })
  })
})
```

#### E2E 测试 (10%)

**覆盖范围**:
- 关键用户路径
- 跨浏览器兼容性
- 性能测试

**测试工具**:
- Playwright: E2E 测试框架
- Lighthouse CI: 性能测试

**示例**:
```typescript
// e2e/app-discovery.spec.ts
import { test, expect } from '@playwright/test'

test.describe('App Discovery', () => {
  test('user can browse and filter apps', async ({ page }) => {
    await page.goto('/')

    // 验证首页加载
    await expect(page.locator('h1')).toContainText('AI App Store')

    // 浏览应用列表
    await page.click('text=浏览应用')
    await expect(page).toHaveURL('/apps')

    // 使用筛选器
    await page.selectOption('[data-testid="category-filter"]', 'productivity')
    await page.waitForLoadState('networkidle')

    // 验证筛选结果
    const appCards = page.locator('[data-testid="app-card"]')
    await expect(appCards).toHaveCountGreaterThan(0)

    // 点击应用查看详情
    await appCards.first().click()
    await expect(page.locator('[data-testid="app-detail"]')).toBeVisible()
  })

  test('user can search for apps', async ({ page }) => {
    await page.goto('/apps')

    // 搜索应用
    await page.fill('[data-testid="search-input"]', 'chat')
    await page.press('[data-testid="search-input"]', 'Enter')

    // 验证搜索结果
    await expect(page.locator('[data-testid="search-results"]')).toBeVisible()
    const results = page.locator('[data-testid="app-card"]')
    await expect(results).toHaveCountGreaterThan(0)
  })
})
```

### 测试配置

#### Jest 配置

```javascript
// jest.config.js
const nextJest = require('next/jest')

const createJestConfig = nextJest({
  dir: './',
})

const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  testEnvironment: 'jest-environment-jsdom',
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/pages/_app.tsx',
    '!src/pages/_document.tsx',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
}

module.exports = createJestConfig(customJestConfig)
```

#### 测试工具配置

```typescript
// tests/utils/TestWrapper.tsx
import React from 'react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { AuthProvider } from '@/contexts/AuthContext'

const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

export const TestWrapper: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const queryClient = createTestQueryClient()

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        {children}
      </AuthProvider>
    </QueryClientProvider>
  )
}

// tests/mocks/handlers.ts
import { rest } from 'msw'
import { mockApps, mockUsers } from './data'

export const handlers = [
  rest.get('/api/apps', (req, res, ctx) => {
    const page = req.url.searchParams.get('page') || '1'
    const perPage = req.url.searchParams.get('per_page') || '20'
    
    return res(
      ctx.json({
        success: true,
        data: {
          apps: mockApps,
          pagination: {
            page: parseInt(page),
            per_page: parseInt(perPage),
            total: mockApps.length,
            pages: 1,
            has_next: false,
            has_prev: false,
          },
        },
      })
    )
  }),

  rest.get('/api/users', (req, res, ctx) => {
    return res(
      ctx.json({
        success: true,
        data: {
          users: mockUsers,
          pagination: {
            page: 1,
            per_page: 20,
            total: mockUsers.length,
            pages: 1,
            has_next: false,
            has_prev: false,
          },
        },
      })
    )
  }),
]
```

### 持续集成

#### GitHub Actions 配置

```yaml
# .github/workflows/test.yml
name: Test

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [18.x, 20.x]

    steps:
      - uses: actions/checkout@v3

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run type check
        run: npm run type-check

      - name: Run linting
        run: npm run lint

      - name: Run unit tests
        run: npm run test:coverage

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3

  e2e:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - name: Use Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18.x'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install Playwright
        run: npx playwright install --with-deps

      - name: Build application
        run: npm run build

      - name: Run E2E tests
        run: npm run test:e2e

      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-report
          path: playwright-report/
```

---

**文档完成** ✅

本前端产品需求文档为 AI App Store 项目提供了完整的前端开发指南，涵盖了从技术架构到部署配置的所有关键方面。文档遵循现代 Web 开发最佳实践，特别针对 Vercel 部署平台进行了优化，确保项目能够高效、安全地运行在生产环境中。

