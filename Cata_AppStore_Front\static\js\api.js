// API 配置和工具函数
const API_BASE_URL = 'http://localhost:6101/api';

// API 工具函数
class API {
    static async request(endpoint, options = {}) {
        const url = `${API_BASE_URL}${endpoint}`;
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            },
        };

        // 如果有认证令牌，添加到请求头
        const token = localStorage.getItem('token');
        if (token) {
            defaultOptions.headers.Authorization = `Bearer ${token}`;
        }

        const config = { ...defaultOptions, ...options };

        try {
            const response = await fetch(url, config);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || '请求失败');
            }

            return data;
        } catch (error) {
            console.error('API Error:', error);
            throw error;
        }
    }

    static async get(endpoint) {
        return this.request(endpoint, { method: 'GET' });
    }

    static async post(endpoint, data) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data),
        });
    }

    static async put(endpoint, data) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data),
        });
    }

    static async delete(endpoint) {
        return this.request(endpoint, { method: 'DELETE' });
    }

    static async upload(endpoint, formData) {
        const url = `${API_BASE_URL}${endpoint}`;
        const token = localStorage.getItem('token');

        const config = {
            method: 'POST',
            body: formData,
        };

        if (token) {
            config.headers = {
                Authorization: `Bearer ${token}`,
            };
        }

        try {
            const response = await fetch(url, config);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || '上传失败');
            }

            return data;
        } catch (error) {
            console.error('Upload Error:', error);
            throw error;
        }
    }
}

// 认证相关 API
const AuthAPI = {
    async login(username, password) {
        const response = await API.post('/login', { username, password });
        if (response.token) {
            localStorage.setItem('token', response.token);
            localStorage.setItem('user', JSON.stringify(response.user));
        }
        return response;
    },

    async register(userData) {
        return await API.post('/register', userData);
    },

    logout() {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
    },

    getCurrentUser() {
        const user = localStorage.getItem('user');
        return user ? JSON.parse(user) : null;
    },

    getToken() {
        return localStorage.getItem('token');
    },

    isAuthenticated() {
        return !!this.getToken();
    }
};

// 应用相关 API
const AppAPI = {
    async getApps(params = {}) {
        const queryParams = new URLSearchParams(params).toString();
        return await API.get(`/apps${queryParams ? `?${queryParams}` : ''}`);
    },

    async getApp(id) {
        return await API.get(`/apps/${id}`);
    },

    async createApp(formData) {
        return await API.upload('/apps', formData);
    },

    async updateApp(id, data) {
        return await API.put(`/apps/${id}`, data);
    },

    async deleteApp(id) {
        return await API.delete(`/apps/${id}`);
    },

    async getMyApps() {
        return await API.get('/my-apps');
    },

    async downloadApp(id) {
        return await API.get(`/apps/${id}/download`);
    },

    async getAppFiles(id) {
        return await API.get(`/apps/${id}/files`);
    }
};

// 分类相关 API
const CategoryAPI = {
    async getCategories() {
        return await API.get('/categories');
    }
};

// 管理员相关 API
const AdminAPI = {
    async getDashboard() {
        return await API.get('/admin/dashboard');
    },

    async getAllApps(params = {}) {
        const queryParams = new URLSearchParams(params).toString();
        return await API.get(`/admin/apps${queryParams ? `?${queryParams}` : ''}`);
    },

    async approveApp(id) {
        return await API.post(`/admin/apps/${id}/approve`);
    },

    async rejectApp(id, reason) {
        return await API.post(`/admin/apps/${id}/reject`, { reason });
    },

    async featureApp(id, featured) {
        return await API.post(`/admin/apps/${id}/feature`, { featured });
    },

    async getUsers() {
        return await API.get('/admin/users');
    },

    async updateUser(id, data) {
        return await API.put(`/admin/users/${id}`, data);
    },

    async deleteUser(id) {
        return await API.delete(`/admin/users/${id}`);
    },

    async createCategory(data) {
        return await API.post('/admin/categories', data);
    },

    async getSystemStats() {
        return await API.get('/admin/system/stats');
    }
};

// 导出 API 对象
window.API = API;
window.AuthAPI = AuthAPI;
window.AppAPI = AppAPI;
window.CategoryAPI = CategoryAPI;
window.AdminAPI = AdminAPI;