# 应用上传和文件处理系统

## 1. 系统概述

基于 Wix Media Manager 和 Wix Data API，实现完整的应用上传、文件处理和自动解压功能。

### 核心功能
1. **文件上传**：支持 ZIP 文件上传到 Wix Media Manager
2. **自动解压**：后端自动解压 ZIP 文件并提取内容
3. **文件管理**：管理解压后的文件和应用信息
4. **权限控制**：基于用户角色的上传权限管理

## 2. 技术架构

### 2.1 文件上传流程
```
用户选择文件 → 前端验证 → 生成上传URL → 上传到Media Manager → 触发后端处理 → 解压文件 → 更新数据库
```

### 2.2 组件架构
```
Frontend (React)
├── UploadForm.jsx          // 上传表单组件
├── FileUploader.jsx        // 文件上传器
├── ProgressBar.jsx         // 上传进度条
└── AppPreview.jsx          // 应用预览组件

Backend (Wix Code)
├── upload-handler.js       // 上传处理器
├── file-processor.js       // 文件处理器
├── zip-extractor.js        // ZIP解压器
└── app-manager.js          // 应用管理器
```

## 3. 前端实现

### 3.1 文件上传组件
```jsx
// components/FileUploader.jsx
import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { generateFileUploadUrl, uploadFile } from '../utils/fileUpload';

export function FileUploader({ onUploadComplete, onUploadProgress }) {
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);

  const onDrop = useCallback(async (acceptedFiles) => {
    const file = acceptedFiles[0];
    
    if (!file) return;
    
    // 验证文件类型和大小
    if (!file.name.endsWith('.zip')) {
      alert('请选择 ZIP 文件');
      return;
    }
    
    if (file.size > 50 * 1024 * 1024) { // 50MB 限制
      alert('文件大小不能超过 50MB');
      return;
    }
    
    try {
      setUploading(true);
      setProgress(0);
      
      // 生成上传 URL
      const uploadUrl = await generateFileUploadUrl(file.type, file.name);
      
      // 上传文件
      const result = await uploadFile(uploadUrl, file, (progressEvent) => {
        const percentCompleted = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total
        );
        setProgress(percentCompleted);
        onUploadProgress?.(percentCompleted);
      });
      
      onUploadComplete?.(result);
    } catch (error) {
      console.error('Upload failed:', error);
      alert('上传失败，请重试');
    } finally {
      setUploading(false);
    }
  }, [onUploadComplete, onUploadProgress]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/zip': ['.zip']
    },
    multiple: false,
    disabled: uploading
  });

  return (
    <div 
      {...getRootProps()} 
      className={`upload-zone ${isDragActive ? 'active' : ''} ${uploading ? 'uploading' : ''}`}
    >
      <input {...getInputProps()} />
      {uploading ? (
        <div className="upload-progress">
          <div className="progress-bar">
            <div 
              className="progress-fill" 
              style={{ width: `${progress}%` }}
            />
          </div>
          <p>上传中... {progress}%</p>
        </div>
      ) : (
        <div className="upload-prompt">
          <i className="upload-icon">📁</i>
          <p>
            {isDragActive 
              ? '拖放 ZIP 文件到这里' 
              : '点击或拖放 ZIP 文件到这里上传'
            }
          </p>
          <small>支持最大 50MB 的 ZIP 文件</small>
        </div>
      )}
    </div>
  );
}
```

### 3.2 应用上传表单
```jsx
// components/AppUploadForm.jsx
import React, { useState } from 'react';
import { FileUploader } from './FileUploader';
import { createApp } from '../utils/appManager';

export function AppUploadForm({ onSuccess }) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    version: '1.0.0',
    tags: []
  });
  const [uploadedFile, setUploadedFile] = useState(null);
  const [submitting, setSubmitting] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleTagsChange = (e) => {
    const tags = e.target.value.split(',').map(tag => tag.trim()).filter(Boolean);
    setFormData(prev => ({
      ...prev,
      tags
    }));
  };

  const handleFileUpload = (fileResult) => {
    setUploadedFile(fileResult);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!uploadedFile) {
      alert('请先上传 ZIP 文件');
      return;
    }

    try {
      setSubmitting(true);
      
      const appData = {
        ...formData,
        zipFileUrl: uploadedFile.file.url,
        fileSize: uploadedFile.file.sizeInBytes,
        status: 'pending'
      };
      
      const result = await createApp(appData);
      onSuccess?.(result);
      
      // 重置表单
      setFormData({
        title: '',
        description: '',
        category: '',
        version: '1.0.0',
        tags: []
      });
      setUploadedFile(null);
      
    } catch (error) {
      console.error('App creation failed:', error);
      alert('应用创建失败，请重试');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="app-upload-form">
      <div className="form-group">
        <label htmlFor="title">应用标题 *</label>
        <input
          type="text"
          id="title"
          name="title"
          value={formData.title}
          onChange={handleInputChange}
          required
          placeholder="输入应用标题"
        />
      </div>

      <div className="form-group">
        <label htmlFor="description">应用描述</label>
        <textarea
          id="description"
          name="description"
          value={formData.description}
          onChange={handleInputChange}
          rows={4}
          placeholder="描述您的应用功能和特点"
        />
      </div>

      <div className="form-row">
        <div className="form-group">
          <label htmlFor="category">分类</label>
          <select
            id="category"
            name="category"
            value={formData.category}
            onChange={handleInputChange}
          >
            <option value="">选择分类</option>
            <option value="productivity">生产力工具</option>
            <option value="games">游戏</option>
            <option value="utilities">实用工具</option>
            <option value="education">教育</option>
            <option value="business">商务</option>
          </select>
        </div>

        <div className="form-group">
          <label htmlFor="version">版本号</label>
          <input
            type="text"
            id="version"
            name="version"
            value={formData.version}
            onChange={handleInputChange}
            placeholder="1.0.0"
          />
        </div>
      </div>

      <div className="form-group">
        <label htmlFor="tags">标签</label>
        <input
          type="text"
          id="tags"
          name="tags"
          value={formData.tags.join(', ')}
          onChange={handleTagsChange}
          placeholder="用逗号分隔多个标签"
        />
      </div>

      <div className="form-group">
        <label>应用文件 *</label>
        <FileUploader 
          onUploadComplete={handleFileUpload}
        />
        {uploadedFile && (
          <div className="file-info">
            <p>✅ 文件已上传: {uploadedFile.file.displayName}</p>
            <p>大小: {(uploadedFile.file.sizeInBytes / 1024 / 1024).toFixed(2)} MB</p>
          </div>
        )}
      </div>

      <button 
        type="submit" 
        disabled={submitting || !uploadedFile}
        className="submit-button"
      >
        {submitting ? '创建中...' : '创建应用'}
      </button>
    </form>
  );
}
```

## 4. 后端实现

### 4.1 文件上传工具函数
```javascript
// utils/fileUpload.js
import { files } from '@wix/media';
import axios from 'axios';

export async function generateFileUploadUrl(mimeType, fileName) {
  try {
    const response = await files.generateFileUploadUrl(mimeType, {
      fileName: fileName
    });
    return response.uploadUrl;
  } catch (error) {
    console.error('Failed to generate upload URL:', error);
    throw new Error('无法生成上传链接');
  }
}

export async function uploadFile(uploadUrl, file, onProgress) {
  try {
    const response = await axios.put(uploadUrl, file, {
      headers: {
        'Content-Type': file.type
      },
      params: {
        filename: file.name
      },
      onUploadProgress: onProgress
    });
    
    return response.data;
  } catch (error) {
    console.error('File upload failed:', error);
    throw new Error('文件上传失败');
  }
}
```

### 4.2 ZIP 文件处理器
```javascript
// backend/zip-processor.js
import { files } from '@wix/media';
import { items } from '@wix/data';
import JSZip from 'jszip';

export async function processZipFile(fileId, appId) {
  try {
    // 获取文件内容
    const fileData = await files.getFileDescriptor(fileId);
    const fileContent = await fetch(fileData.url).then(res => res.arrayBuffer());
    
    // 解压 ZIP 文件
    const zip = new JSZip();
    const zipContent = await zip.loadAsync(fileContent);
    
    const extractedFiles = [];
    const filePromises = [];
    
    // 遍历 ZIP 文件中的所有文件
    zipContent.forEach((relativePath, file) => {
      if (!file.dir) {
        filePromises.push(
          file.async('blob').then(async (blob) => {
            // 为每个文件生成上传 URL
            const mimeType = getMimeType(relativePath);
            const uploadUrl = await files.generateFileUploadUrl(mimeType, {
              fileName: relativePath,
              parentFolderId: `app-${appId}`
            });
            
            // 上传解压后的文件
            const uploadResponse = await fetch(uploadUrl, {
              method: 'PUT',
              body: blob,
              headers: {
                'Content-Type': mimeType
              }
            });
            
            if (uploadResponse.ok) {
              const result = await uploadResponse.json();
              extractedFiles.push({
                path: relativePath,
                fileId: result.file.id,
                url: result.file.url,
                size: result.file.sizeInBytes
              });
            }
          })
        );
      }
    });
    
    // 等待所有文件上传完成
    await Promise.all(filePromises);
    
    // 更新应用记录
    await items.updateDataItem('Apps', appId, {
      extractedFiles: extractedFiles,
      status: 'approved',
      downloadUrl: generateDownloadUrl(extractedFiles)
    });
    
    return {
      success: true,
      extractedFiles: extractedFiles
    };
    
  } catch (error) {
    console.error('ZIP processing failed:', error);
    
    // 更新应用状态为失败
    await items.updateDataItem('Apps', appId, {
      status: 'failed',
      errorMessage: error.message
    });
    
    throw error;
  }
}

function getMimeType(filename) {
  const ext = filename.split('.').pop().toLowerCase();
  const mimeTypes = {
    'html': 'text/html',
    'css': 'text/css',
    'js': 'application/javascript',
    'json': 'application/json',
    'png': 'image/png',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'gif': 'image/gif',
    'svg': 'image/svg+xml',
    'txt': 'text/plain',
    'md': 'text/markdown'
  };
  return mimeTypes[ext] || 'application/octet-stream';
}

function generateDownloadUrl(extractedFiles) {
  // 查找主入口文件
  const indexFile = extractedFiles.find(file => 
    file.path === 'index.html' || 
    file.path.endsWith('/index.html')
  );
  
  return indexFile ? indexFile.url : extractedFiles[0]?.url;
}
```

### 4.3 应用管理器
```javascript
// utils/appManager.js
import { items } from '@wix/data';
import { authentication } from '@wix/members';
import { members } from '@wix/members';

export async function createApp(appData) {
  try {
    // 获取当前用户信息
    const currentUser = await getCurrentUser();
    if (!currentUser) {
      throw new Error('用户未登录');
    }
    
    // 检查用户上传配额
    await checkUploadQuota(currentUser.id);
    
    // 创建应用记录
    const appItem = {
      ...appData,
      uploaderId: currentUser.id,
      uploaderEmail: currentUser.email,
      status: 'processing',
      downloadCount: 0,
      _owner: currentUser.id
    };
    
    const result = await items.insertDataItem('Apps', appItem);
    
    // 异步处理 ZIP 文件
    processZipFileAsync(appData.zipFileUrl, result._id);
    
    // 更新用户上传计数
    await updateUserUploadCount(currentUser.id);
    
    return result;
    
  } catch (error) {
    console.error('App creation failed:', error);
    throw error;
  }
}

async function getCurrentUser() {
  try {
    const isLoggedIn = await authentication.loggedIn();
    if (!isLoggedIn) return null;
    
    const currentMember = await members.getCurrentMember();
    return {
      id: currentMember._id,
      email: currentMember.loginEmail,
      customFields: currentMember.customFields
    };
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}

async function checkUploadQuota(userId) {
  const userApps = await items.queryDataItems('Apps')
    .eq('uploaderId', userId)
    .find();
  
  const uploadQuota = 10; // 默认配额
  
  if (userApps.items.length >= uploadQuota) {
    throw new Error('已达到上传配额限制');
  }
}

async function updateUserUploadCount(userId) {
  try {
    const member = await members.getMember(userId);
    const currentCount = member.customFields?.uploadedAppsCount || 0;
    
    await members.updateMember(userId, {
      customFields: {
        ...member.customFields,
        uploadedAppsCount: currentCount + 1
      }
    });
  } catch (error) {
    console.error('Failed to update user upload count:', error);
  }
}

async function processZipFileAsync(zipFileUrl, appId) {
  try {
    // 从 URL 提取文件 ID
    const fileId = extractFileIdFromUrl(zipFileUrl);
    
    // 异步处理 ZIP 文件
    setTimeout(async () => {
      try {
        await processZipFile(fileId, appId);
      } catch (error) {
        console.error('Async ZIP processing failed:', error);
      }
    }, 1000);
    
  } catch (error) {
    console.error('Failed to start async processing:', error);
  }
}

function extractFileIdFromUrl(url) {
  // 从 Wix Media URL 中提取文件 ID
  const matches = url.match(/\/([^\/]+)$/);
  return matches ? matches[1] : null;
}
```

## 5. 文件处理事件监听

### 5.1 文件就绪事件处理
```javascript
// backend/file-events.js
import { files } from '@wix/media';
import { items } from '@wix/data';

// 监听文件就绪事件
files.onFileDescriptorFileReady(async (event) => {
  const { fileId } = event;
  
  try {
    // 查找相关的应用记录
    const apps = await items.queryDataItems('Apps')
      .contains('zipFileUrl', fileId)
      .find();
    
    if (apps.items.length > 0) {
      const app = apps.items[0];
      
      // 开始处理 ZIP 文件
      await processZipFile(fileId, app._id);
    }
    
  } catch (error) {
    console.error('File ready event processing failed:', error);
  }
});

// 监听文件失败事件
files.onFileDescriptorFileFailed(async (event) => {
  const { fileId, reason } = event;
  
  try {
    // 查找相关的应用记录
    const apps = await items.queryDataItems('Apps')
      .contains('zipFileUrl', fileId)
      .find();
    
    if (apps.items.length > 0) {
      const app = apps.items[0];
      
      // 更新应用状态为失败
      await items.updateDataItem('Apps', app._id, {
        status: 'failed',
        errorMessage: `文件处理失败: ${reason}`
      });
    }
    
  } catch (error) {
    console.error('File failed event processing failed:', error);
  }
});
```

## 6. 安全和验证

### 6.1 文件验证
```javascript
// utils/fileValidation.js
export function validateZipFile(file) {
  const errors = [];
  
  // 检查文件类型
  if (!file.type.includes('zip') && !file.name.endsWith('.zip')) {
    errors.push('只支持 ZIP 格式文件');
  }
  
  // 检查文件大小 (50MB 限制)
  const maxSize = 50 * 1024 * 1024;
  if (file.size > maxSize) {
    errors.push('文件大小不能超过 50MB');
  }
  
  // 检查文件名
  if (file.name.length > 255) {
    errors.push('文件名过长');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

export function validateAppData(appData) {
  const errors = [];
  
  // 检查必填字段
  if (!appData.title || appData.title.trim().length === 0) {
    errors.push('应用标题不能为空');
  }
  
  if (appData.title && appData.title.length > 100) {
    errors.push('应用标题不能超过 100 个字符');
  }
  
  if (appData.description && appData.description.length > 1000) {
    errors.push('应用描述不能超过 1000 个字符');
  }
  
  // 检查版本号格式
  const versionRegex = /^\d+\.\d+\.\d+$/;
  if (appData.version && !versionRegex.test(appData.version)) {
    errors.push('版本号格式不正确，应为 x.y.z 格式');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}
```

## 7. 用户界面样式

### 7.1 上传组件样式
```css
/* styles/upload.css */
.upload-zone {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
}

.upload-zone:hover {
  border-color: #007bff;
  background: #f0f8ff;
}

.upload-zone.active {
  border-color: #007bff;
  background: #e6f3ff;
}

.upload-zone.uploading {
  cursor: not-allowed;
  opacity: 0.7;
}

.upload-icon {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.upload-progress {
  max-width: 300px;
  margin: 0 auto;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: #007bff;
  transition: width 0.3s ease;
}

.app-upload-form {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.file-info {
  margin-top: 10px;
  padding: 10px;
  background: #e8f5e8;
  border-radius: 4px;
  font-size: 14px;
}

.submit-button {
  width: 100%;
  padding: 12px 24px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.submit-button:hover:not(:disabled) {
  background: #0056b3;
}

.submit-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}
```

## 8. 错误处理和用户反馈

### 8.1 错误处理策略
```javascript
// utils/errorHandler.js
export class AppUploadError extends Error {
  constructor(message, code, details = {}) {
    super(message);
    this.name = 'AppUploadError';
    this.code = code;
    this.details = details;
  }
}

export function handleUploadError(error) {
  console.error('Upload error:', error);
  
  if (error instanceof AppUploadError) {
    switch (error.code) {
      case 'FILE_TOO_LARGE':
        return '文件大小超过限制，请选择小于 50MB 的文件';
      case 'INVALID_FILE_TYPE':
        return '不支持的文件类型，请选择 ZIP 文件';
      case 'QUOTA_EXCEEDED':
        return '您已达到上传配额限制';
      case 'NETWORK_ERROR':
        return '网络连接失败，请检查网络后重试';
      default:
        return error.message || '上传失败，请重试';
    }
  }
  
  return '发生未知错误，请重试';
}
```

## 9. 性能优化

### 9.1 文件上传优化
- 使用分片上传处理大文件
- 实现断点续传功能
- 压缩文件预处理
- 并行处理多个文件

### 9.2 解压处理优化
- 异步处理避免阻塞
- 限制并发解压任务数量
- 实现处理队列机制
- 添加处理超时控制

## 10. 监控和日志

### 10.1 上传监控
```javascript
// utils/uploadMonitoring.js
export function trackUploadEvent(eventType, data) {
  console.log(`Upload Event: ${eventType}`, data);
  
  // 发送到分析服务
  // analytics.track(eventType, data);
}

export function logUploadError(error, context) {
  console.error('Upload Error:', {
    error: error.message,
    stack: error.stack,
    context
  });
  
  // 发送错误报告
  // errorReporting.captureException(error, context);
}
```

这个文件上传和处理系统提供了完整的应用上传功能，包括前端文件选择、上传进度显示、后端文件处理、ZIP解压、错误处理等所有必要组件。系统设计考虑了安全性、性能和用户体验，能够满足应用商店的文件管理需求。

